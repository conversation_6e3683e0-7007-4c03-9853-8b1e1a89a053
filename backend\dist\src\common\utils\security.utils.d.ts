export declare class SecurityUtils {
    static generateSecureToken(length?: number): string;
    static hashString(str: string, salt?: string): Promise<string>;
    static validatePasswordStrength(password: string): {
        isValid: boolean;
        errors: string[];
    };
    static sanitizeInput(input: string): string;
    static createRateLimiter(windowMs: number, maxRequests: number): (ip: string) => boolean;
}
