"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnitQuiz = void 0;
const typeorm_1 = require("typeorm");
const unit_entity_1 = require("../../entities/unit.entity");
const quiz_question_entity_1 = require("../../entities/quiz-question.entity");
let UnitQuiz = class UnitQuiz {
};
exports.UnitQuiz = UnitQuiz;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], UnitQuiz.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => unit_entity_1.Unit, unit => unit.unitQuizzes),
    __metadata("design:type", unit_entity_1.Unit)
], UnitQuiz.prototype, "unit", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], UnitQuiz.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)('text'),
    __metadata("design:type", String)
], UnitQuiz.prototype, "instructions", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => quiz_question_entity_1.QuizQuestion, (q) => q.unitQuiz, { cascade: true }),
    __metadata("design:type", Array)
], UnitQuiz.prototype, "questions", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], UnitQuiz.prototype, "isPublished", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], UnitQuiz.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], UnitQuiz.prototype, "updatedAt", void 0);
exports.UnitQuiz = UnitQuiz = __decorate([
    (0, typeorm_1.Entity)('unit_quizzes')
], UnitQuiz);
//# sourceMappingURL=unit-quiz.entity.js.map