"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MaterialsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const materials_entity_1 = require("../../entities/materials.entity");
const uuid_1 = require("uuid");
const config_1 = require("@nestjs/config");
const material_shares_entity_1 = require("../../entities/material_shares.entity");
const client_s3_1 = require("@aws-sdk/client-s3");
let MaterialsService = class MaterialsService {
    constructor(materialsRepository, materialShareRepository, configService) {
        this.materialsRepository = materialsRepository;
        this.materialShareRepository = materialShareRepository;
        this.configService = configService;
        this.s3 = new client_s3_1.S3Client({
            credentials: {
                accessKeyId: this.configService.get('AWS_ACCESS_KEY_ID'),
                secretAccessKey: this.configService.get('AWS_SECRET_ACCESS_KEY'),
            },
            region: this.configService.get('AWS_REGION'),
        });
    }
    async create(material) {
        const newMaterial = this.materialsRepository.create(material);
        return this.materialsRepository.save(newMaterial);
    }
    async findAll() {
        return this.materialsRepository.find({ relations: ['uploadedBy', 'unit'] });
    }
    async findOne(id) {
        const material = await this.materialsRepository.findOne({
            where: { id },
            relations: ['uploadedBy', 'unit'],
        });
        if (!material) {
            throw new common_1.NotFoundException(`Material with ID ${id} not found`);
        }
        return material;
    }
    async remove(id) {
        const result = await this.materialsRepository.delete(id);
        if (result.affected === 0) {
            throw new common_1.NotFoundException(`Material with ID ${id} not found`);
        }
    }
    async uploadFile(file, userId, unitId, title, description, type) {
        if (!file)
            throw new Error('No file uploaded');
        const allowedMimeTypes = [
            'application/pdf',
            'image/jpeg',
            'image/png',
            'image/gif',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'text/plain',
            'application/zip',
            'application/x-rar-compressed'
        ];
        if (!allowedMimeTypes.includes(file.mimetype)) {
            console.error(`[MATERIALS-SERVICE] Invalid file type: ${file.mimetype}`);
            throw new Error(`Invalid file type. Allowed types: PDF, images, Office documents, text, zip, and rar files.`);
        }
        const maxSize = 10 * 1024 * 1024;
        if (file.size > maxSize) {
            console.error(`[MATERIALS-SERVICE] File too large: ${file.size} bytes`);
            throw new Error(`File too large. Maximum size is 10MB.`);
        }
        const bucket = this.configService.get('AWS_S3_BUCKET');
        if (!bucket)
            throw new Error('AWS S3 bucket not configured');
        const sanitizedFilename = file.originalname.replace(/[^\w\s.-]/g, '');
        const fileKey = `materials/${(0, uuid_1.v4)()}-${sanitizedFilename}`;
        console.log(`[MATERIALS-SERVICE] Uploading file: ${sanitizedFilename}, size: ${file.size} bytes, type: ${file.mimetype}`);
        const command = new client_s3_1.PutObjectCommand({
            Bucket: bucket,
            Key: fileKey,
            Body: file.buffer,
            ContentType: file.mimetype,
            Metadata: {
                'x-amz-meta-original-filename': sanitizedFilename,
                'x-amz-meta-upload-date': new Date().toISOString(),
                'x-amz-meta-user-id': userId
            }
        });
        const uploadResult = await this.s3.send(command);
        const fileUrl = `https://${bucket}.s3.${this.configService.get('AWS_REGION')}.amazonaws.com/${fileKey}`;
        console.log(`[MATERIALS-SERVICE] File uploaded successfully: ${fileUrl}`);
        const material = this.materialsRepository.create({
            title: title || sanitizedFilename,
            description,
            type: type || materials_entity_1.MaterialType.ARTICLE,
            file_url: fileUrl,
            author: { id: userId },
            unit: { id: unitId },
        });
        return this.materialsRepository.save(material);
    }
    async shareMaterial(materialId, sharedByUserId, sharedWithUserId) {
        const share = this.materialShareRepository.create({
            material: { id: materialId },
            shared_by_user: { id: sharedByUserId },
            shared_with_user: { id: sharedWithUserId },
        });
        return this.materialShareRepository.save(share);
    }
    async findSharedMaterials(userId) {
        return this.materialShareRepository.find({
            where: { shared_with_user: { id: userId } },
            relations: ['material', 'shared_by_user'],
        });
    }
    async findProgressByMaterialId(materialId) {
        return this.materialsRepository.findOne({
            where: { id: materialId },
            relations: ['progress'],
        }).then((material) => material?.progress || []);
    }
    async findProgressByUserId(userId) {
        return this.materialsRepository.findOne({
            where: { user: { id: userId } },
            relations: ['progress'],
        }).then((material) => material?.progress || []);
    }
};
exports.MaterialsService = MaterialsService;
exports.MaterialsService = MaterialsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(materials_entity_1.Material)),
    __param(1, (0, typeorm_1.InjectRepository)(material_shares_entity_1.MaterialShare)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, config_1.ConfigService])
], MaterialsService);
//# sourceMappingURL=materials.service.js.map