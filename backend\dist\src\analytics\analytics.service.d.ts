import { Repository } from "typeorm";
import { Progress } from "../entities/progress.entity";
import { UserResponse } from "../entities/user-response.entity";
import { StudyEvent } from '../entities/study-event.entity';
import { Flashcard } from '../entities/flashcard.entity';
import { QuizAttempt } from '../entities/quiz-attempt.entity';
export declare class AnalyticsService {
    private progressRepository;
    private userResponseRepository;
    private studyEventRepository;
    private flashcardRepository;
    private quizAttemptRepository;
    private predictionModel;
    private readonly modelPath;
    constructor(progressRepository: Repository<Progress>, userResponseRepository: Repository<UserResponse>, studyEventRepository: Repository<StudyEvent>, flashcardRepository: Repository<Flashcard>, quizAttemptRepository: Repository<QuizAttempt>);
    private initializeModel;
    private createPredictionModel;
    trackEvent(eventData: {
        user_id: string;
        event_type: string;
        data: any;
        timestamp: string;
    }): Promise<any>;
    batchTrackEvents(events: Array<{
        user_id: string;
        event_type: string;
        data: any;
        timestamp: string;
    }>): Promise<any>;
    calculatePerformanceMetrics(user_id: string): Promise<{
        totalCards: any;
        cardsDue: any;
        averageInterval: number;
        successRate: number;
        quizPerformance: any;
        studyStreak: number;
    }>;
    generatePredictions(user_id: string): Promise<{
        predictedSuccessRate: any;
        confidence: number;
        recommendedStudyTime: number;
    }>;
    analyzeStudyPatterns(user_id: string): Promise<{
        preferredStudyTimes: any;
        studyDuration: any;
        performanceByTopic: any;
        consistencyScore: number;
    }>;
    generateRecommendations(user_id: string): Promise<{
        studySchedule: any;
        focusAreas: string[];
        improvementTips: string[];
    }>;
    private calculateAverageInterval;
    private calculateSuccessRate;
    private calculateQuizPerformance;
    private calculateStudyStreak;
    private extractFeatures;
    private calculateConfidence;
    private calculateRecommendedStudyTime;
    private analyzePreferredStudyTimes;
    private analyzeStudyDuration;
    private analyzePerformanceByTopic;
    private calculateConsistencyScore;
    private generateStudySchedule;
    private identifyFocusAreas;
    private generateImprovementTips;
    private calculateImprovement;
}
