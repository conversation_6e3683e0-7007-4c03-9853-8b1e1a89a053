"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FlashcardsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const flashcard_entity_1 = require("../entities/flashcard.entity");
const quiz_question_entity_1 = require("../entities/quiz-question.entity");
const typeorm_3 = require("typeorm");
let FlashcardsService = class FlashcardsService {
    constructor(flashcardRepository, quizQuestionRepository) {
        this.flashcardRepository = flashcardRepository;
        this.quizQuestionRepository = quizQuestionRepository;
    }
    async createFlashcard(userId, questionId) {
        const question = await this.quizQuestionRepository.findOne({
            where: { id: questionId }
        });
        if (!question) {
            throw new common_1.NotFoundException('Question not found');
        }
        const flashcard = this.flashcardRepository.create({
            user: { id: userId },
            question: { id: questionId },
            ease_factor: 2.5,
            interval: 1,
            next_review: new Date(),
            correct_streak: 0,
            last_review: new Date()
        });
        return this.flashcardRepository.save(flashcard);
    }
    async getDueCards(userId) {
        return this.flashcardRepository.find({
            where: {
                user: { id: userId },
                next_review: (0, typeorm_3.LessThanOrEqual)(new Date())
            },
            relations: ['question'],
            order: {
                next_review: 'ASC'
            }
        });
    }
    async updateCard(cardId, quality) {
        const card = await this.flashcardRepository.findOne({
            where: { id: cardId }
        });
        if (!card) {
            throw new common_1.NotFoundException('Flashcard not found');
        }
        const newEf = Math.max(1.3, card.ease_factor + (0.1 - (5 - quality) * (0.08 + (5 - quality) * 0.02)));
        let newInterval;
        if (quality >= 3) {
            if (card.correct_streak === 0) {
                newInterval = 1;
            }
            else if (card.correct_streak === 1) {
                newInterval = 6;
            }
            else {
                newInterval = Math.round(card.interval * newEf);
            }
            card.correct_streak += 1;
        }
        else {
            card.correct_streak = 0;
            newInterval = 1;
        }
        card.ease_factor = newEf;
        card.interval = newInterval;
        card.next_review = new Date(Date.now() + newInterval * 24 * 60 * 60 * 1000);
        card.last_review = new Date();
        return this.flashcardRepository.save(card);
    }
    async getCardStats(userId) {
        const cards = await this.flashcardRepository.find({
            where: { user: { id: userId } }
        });
        const now = new Date();
        const dueCards = cards.filter((card) => card.next_review <= now);
        const upcomingCards = cards.filter((card) => card.next_review > now);
        return {
            total: cards.length,
            due: dueCards.length,
            upcoming: upcomingCards.length,
            averageInterval: cards.reduce((sum, card) => sum + card.interval, 0) / cards.length,
        };
    }
    async syncCards(userId, cards) {
        const syncedCards = [];
        for (const card of cards) {
            const existingCard = await this.flashcardRepository.findOne({
                where: {
                    user: { id: userId },
                    question: { id: card.question?.id }
                }
            });
            if (existingCard) {
                if (existingCard.updated_at && card.updated_at && existingCard.updated_at > card.updated_at) {
                    syncedCards.push(existingCard);
                }
                else {
                    const updatedCard = await this.flashcardRepository.save({
                        ...existingCard,
                        ...card,
                        user: { id: userId },
                        question: { id: card.question?.id }
                    });
                    syncedCards.push(updatedCard);
                }
            }
            else {
                const newCard = await this.createFlashcard(userId, card.question?.id || '');
                syncedCards.push(newCard);
            }
        }
        return syncedCards;
    }
};
exports.FlashcardsService = FlashcardsService;
exports.FlashcardsService = FlashcardsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(flashcard_entity_1.Flashcard)),
    __param(1, (0, typeorm_1.InjectRepository)(quiz_question_entity_1.QuizQuestion)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], FlashcardsService);
//# sourceMappingURL=flashcards.service.js.map