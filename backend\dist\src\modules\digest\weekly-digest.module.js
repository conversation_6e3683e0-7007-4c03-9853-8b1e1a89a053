"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeeklyDigestModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const weekly_digest_service_1 = require("./weekly-digest.service");
const weekly_digest_controller_1 = require("./weekly-digest.controller");
const user_entity_1 = require("../../entities/user.entity");
const materials_entity_1 = require("../../entities/materials.entity");
const cpd_tracking_entity_1 = require("../../entities/cpd-tracking.entity");
const weekly_digest_entity_1 = require("../../entities/weekly-digest.entity");
let WeeklyDigestModule = class WeeklyDigestModule {
};
exports.WeeklyDigestModule = WeeklyDigestModule;
exports.WeeklyDigestModule = WeeklyDigestModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                user_entity_1.User,
                materials_entity_1.Material,
                cpd_tracking_entity_1.CPDActivity,
                weekly_digest_entity_1.WeeklyDigest,
            ]),
        ],
        providers: [weekly_digest_service_1.WeeklyDigestService],
        controllers: [weekly_digest_controller_1.WeeklyDigestController],
        exports: [weekly_digest_service_1.WeeklyDigestService],
    })
], WeeklyDigestModule);
//# sourceMappingURL=weekly-digest.module.js.map