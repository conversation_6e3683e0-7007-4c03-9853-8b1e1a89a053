import { User } from './user.entity';
import { Permission } from './permission.entity';
export declare class Role {
    id: string;
    name: string;
    description: string;
    is_active: boolean;
    color: string;
    hierarchy_level: number;
    metadata: {
        description: string;
        usage_stats?: {
            user_count: number;
            last_used: Date;
        };
    };
    users: User[];
    permissions: Permission[];
    created_at: Date;
    updated_at: Date;
}
