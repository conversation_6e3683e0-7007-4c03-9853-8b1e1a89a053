"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnitsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const unit_entity_1 = require("../../entities/unit.entity");
let UnitsService = class UnitsService {
    constructor(unitsRepository) {
        this.unitsRepository = unitsRepository;
    }
    async findAll() {
        return this.unitsRepository.find({
            relations: ['materials', 'progress'],
            order: {
                order_index: 'ASC'
            }
        });
    }
    async findOne(id) {
        const unit = await this.unitsRepository.findOne({
            where: { id },
            relations: ['materials', 'progress']
        });
        if (!unit) {
            throw new common_1.NotFoundException(`Unit with ID ${id} not found`);
        }
        return unit;
    }
    async create(unitData) {
        const newUnit = this.unitsRepository.create(unitData);
        return this.unitsRepository.save(newUnit);
    }
    async update(id, unitData) {
        await this.unitsRepository.update(id, unitData);
        return this.findOne(id);
    }
    async delete(id) {
        const unit = await this.findOne(id);
        await this.unitsRepository.remove(unit);
    }
};
exports.UnitsService = UnitsService;
exports.UnitsService = UnitsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(unit_entity_1.Unit)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object])
], UnitsService);
//# sourceMappingURL=units.service.js.map