import { User } from './user.entity';
export declare class UserSecuritySettings {
    id: string;
    userId: string;
    user: User;
    twoFactorEnabled: boolean;
    twoFactorSecret: string;
    backupCodes: string[];
    securityQuestions: {
        question: string;
        answer: string;
    }[];
    emailNotificationsEnabled: boolean;
    isEmailVerified: boolean;
    emailVerificationToken: string | null;
    emailVerificationExpires: Date | null;
    createdAt: Date;
    updatedAt: Date;
}
export declare class UserSession {
    id: string;
    userId: string;
    user: User;
    token: string;
    deviceId: string;
    deviceType: string;
    ipAddress: string;
    location: string;
    userAgent: string;
    lastAccessed: Date;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export declare class SecurityEvent {
    id: string;
    userId: string;
    user: User;
    eventType: string;
    eventData: any;
    ipAddress: string;
    deviceId: string;
    userAgent: string;
    timestamp: Date;
}
