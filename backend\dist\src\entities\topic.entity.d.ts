import { TopicProgress } from './topic-progress.entity';
import { Unit } from './unit.entity';
export declare class Topic {
    id: string;
    title: string;
    description: string;
    category: string;
    difficulty: number;
    estimatedHours: number;
    isActive: boolean;
    prerequisites: string[];
    resources: {
        type: string;
        url: string;
        title: string;
    }[];
    units: Unit[];
    progress: TopicProgress[];
    createdAt: Date;
    updatedAt: Date;
}
