@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\medical\backend\node_modules\.pnpm\node-gyp@11.2.0\node_modules\node-gyp\bin\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\node-gyp@11.2.0\node_modules\node-gyp\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\node-gyp@11.2.0\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\medical\backend\node_modules\.pnpm\node-gyp@11.2.0\node_modules\node-gyp\bin\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\node-gyp@11.2.0\node_modules\node-gyp\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\node-gyp@11.2.0\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\node-gyp\bin\node-gyp.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\node-gyp\bin\node-gyp.js" %*
)
