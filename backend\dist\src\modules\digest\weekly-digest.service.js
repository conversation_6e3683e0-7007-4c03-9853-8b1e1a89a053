"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeeklyDigestService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_entity_1 = require("../../entities/user.entity");
const materials_entity_1 = require("../../entities/materials.entity");
const cpd_tracking_entity_1 = require("../../entities/cpd-tracking.entity");
const weekly_digest_entity_1 = require("../../entities/weekly-digest.entity");
let WeeklyDigestService = class WeeklyDigestService {
    constructor(userRepository, materialRepository, cpdActivityRepository, weeklyDigestRepository) {
        this.userRepository = userRepository;
        this.materialRepository = materialRepository;
        this.cpdActivityRepository = cpdActivityRepository;
        this.weeklyDigestRepository = weeklyDigestRepository;
    }
    async generateWeeklyDigest(userId) {
        const user = await this.userRepository.findOne({ where: { id: userId } });
        if (!user)
            throw new Error('User not found');
        const endDate = new Date();
        const startDate = new Date(endDate);
        startDate.setDate(startDate.getDate() - 7);
        const activities = await this.cpdActivityRepository.find({
            where: {
                user: { id: userId },
                activity_date: (0, typeorm_2.Between)(startDate, endDate)
            },
            relations: ['material'],
        });
        const newMaterials = await this.materialRepository.find({
            where: {
                createdAt: (0, typeorm_2.MoreThanOrEqual)(startDate),
            },
            order: { createdAt: 'DESC' },
            take: 5,
        });
        const totalPoints = activities.reduce((sum, activity) => sum + activity.points, 0);
        const digest = this.weeklyDigestRepository.create({
            user: { id: userId },
            week_start_date: startDate,
            week_end_date: endDate,
            is_sent: false,
            status: 'pending',
            content: {
                new_materials: newMaterials.map((m) => ({
                    material: m,
                    relevance: 1.0,
                })),
                recommended_topics: [],
                cpd_progress: {
                    points_earned: totalPoints,
                    points_required: 50,
                    activities: activities.map((a) => ({
                        type: a.activityType,
                        points: a.points,
                        date: a.activity_date,
                    })),
                },
                upcoming_deadlines: [],
            },
        });
        return this.weeklyDigestRepository.save(digest);
    }
    async getLatestDigest(userId) {
        return this.weeklyDigestRepository.findOne({
            where: { user: { id: userId } },
            order: { created_at: 'DESC' },
        });
    }
    async markDigestAsRead(digestId) {
        const digest = await this.weeklyDigestRepository.findOne({
            where: { id: digestId },
        });
        if (!digest)
            throw new Error('Digest not found');
        digest.status = 'read';
        digest.read_at = new Date();
        return this.weeklyDigestRepository.save(digest);
    }
    async getUnreadDigestCount(userId) {
        return this.weeklyDigestRepository.count({
            where: {
                user: { id: userId },
                status: 'pending',
            },
        });
    }
    async getRelevantMaterials(user) {
        return this.materialRepository.find({
            where: {},
            order: {
                createdAt: 'DESC'
            },
            take: 10
        });
    }
    calculatePoints(materials) {
        return materials.reduce((acc, material) => {
            return acc + (material.metadata?.cpd_points || 0);
        }, 0);
    }
};
exports.WeeklyDigestService = WeeklyDigestService;
exports.WeeklyDigestService = WeeklyDigestService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(materials_entity_1.Material)),
    __param(2, (0, typeorm_1.InjectRepository)(cpd_tracking_entity_1.CPDActivity)),
    __param(3, (0, typeorm_1.InjectRepository)(weekly_digest_entity_1.WeeklyDigest)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], WeeklyDigestService);
//# sourceMappingURL=weekly-digest.service.js.map