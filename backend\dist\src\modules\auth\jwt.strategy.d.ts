import { ConfigService } from '@nestjs/config';
import { UsersService } from '../users/users.service';
interface JwtPayload {
    sub: string;
    email: string;
    role?: string;
    iat?: number;
    exp?: number;
}
interface AuthenticatedUser {
    id: string;
    email: string;
    role: string;
    is_active: boolean;
}
declare const JwtStrategy_base: any;
export declare class JwtStrategy extends JwtStrategy_base {
    private readonly configService;
    private readonly usersService;
    private readonly logger;
    constructor(configService: ConfigService, usersService: UsersService);
    validate(payload: JwtPayload): Promise<AuthenticatedUser>;
    private validatePayloadStructure;
    private fetchAndValidateUser;
    private performSecurityChecks;
}
export {};
