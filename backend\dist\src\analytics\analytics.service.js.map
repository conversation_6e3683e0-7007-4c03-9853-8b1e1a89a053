{"version": 3, "file": "analytics.service.js", "sourceRoot": "", "sources": ["../../../src/analytics/analytics.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,qCAAqC;AACrC,iEAAuD;AACvD,2EAAgE;AAChE,uEAA4D;AAC5D,mEAAyD;AACzD,yEAA8D;AAC9D,0DAA4C;AAC5C,uCAAyB;AACzB,2CAA6B;AAGtB,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAI3B,YAEE,kBAAgD,EAEhD,sBAAwD,EAExD,oBAAoD,EAEpD,mBAAkD,EAElD,qBAAsD;QAR9C,uBAAkB,GAAlB,kBAAkB,CAAsB;QAExC,2BAAsB,GAAtB,sBAAsB,CAA0B;QAEhD,yBAAoB,GAApB,oBAAoB,CAAwB;QAE5C,wBAAmB,GAAnB,mBAAmB,CAAuB;QAE1C,0BAAqB,GAArB,qBAAqB,CAAyB;QAZvC,cAAS,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,EAAE,wBAAwB,CAAC,CAAC;QAcxF,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,IAAI,CAAC;YAEH,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;gBACnC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YACpD,CAAC;YAED,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;YAC9D,IAAI,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC;gBAEjC,MAAM,SAAS,GAAG,EAAE,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;gBACzD,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;gBACxC,IAAI,CAAC,eAAe,GAAG,MAAM,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,EAAE,CAAC,UAAU,CAAC;oBAC/D,aAAa,EAAE,SAAS,CAAC,aAAa;oBACtC,WAAW,EAAE,SAAS,CAAC,WAAW;oBAClC,UAAU,EAAE,SAAS,CAAC,UAAU;oBAChC,MAAM,EAAE,SAAS,CAAC,MAAM;oBACxB,WAAW,EAAE,SAAS,CAAC,WAAW;oBAClC,WAAW,EAAE,SAAS,CAAC,WAAW;oBAClC,SAAS,EAAE,SAAS,CAAC,SAAS;oBAC9B,mBAAmB,EAAE,SAAS,CAAC,mBAAmB;oBAClD,gBAAgB,EAAE,SAAS,CAAC,gBAAgB;oBAC5C,cAAc,EAAE,SAAS,CAAC,cAAc;iBACzC,CAAC,CAAC,CAAC;YACN,CAAC;iBAAM,CAAC;gBAEN,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAEpD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;gBACtD,EAAE,CAAC,aAAa,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACtD,CAAC;IACH,CAAC;IAEO,qBAAqB;QAC3B,MAAM,KAAK,GAAG,EAAE,CAAC,UAAU,EAAE,CAAC;QAC9B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAChF,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAC5C,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;QAC9D,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;QAChE,KAAK,CAAC,OAAO,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAC;QAC/D,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,SAKhB;QACC,MAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;YAC7C,OAAO,EAAE,SAAS,CAAC,OAAO;YAC1B,UAAU,EAAE,SAAS,CAAC,UAAU;YAChC,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,SAAS,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;SACzC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC/C,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,MAKrB;QACA,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CACrC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;YAC/B,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,UAAU,EAAE,KAAK,CAAC,UAAU;YAC5B,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,SAAS,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;SACrC,CAAC,CACH,CAAC;QACF,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACrD,CAAC;IAED,KAAK,CAAC,2BAA2B,CAAC,OAAe;QAC/C,MAAM,CAAC,UAAU,EAAE,YAAY,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACnD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;YACnE,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;SACtE,CAAC,CAAC;QAEH,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,OAAO,GAAG;YACd,UAAU,EAAE,UAAU,CAAC,MAAM;YAC7B,QAAQ,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,IAAe,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC,CAAC,MAAM;YAC1F,eAAe,EAAE,IAAI,CAAC,wBAAwB,CAAC,UAAU,CAAC;YAC1D,WAAW,EAAE,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC;YAClD,eAAe,EAAE,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC;YAC5D,WAAW,EAAE,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;SACtD,CAAC;QAEF,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,OAAe;QACvC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QACrD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAc,CAAC;QAC5F,MAAM,eAAe,GAAG,MAAM,UAAU,CAAC,IAAI,EAAE,CAAC;QAEhD,OAAO;YACL,oBAAoB,EAAE,eAAe,CAAC,CAAC,CAAC;YACxC,UAAU,EAAE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC;YAC9C,oBAAoB,EAAE,IAAI,CAAC,6BAA6B,CAAC,QAAQ,CAAC;SACnE,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,OAAe;QACxC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;YAClD,KAAK,EAAE,EAAE,OAAO,EAAE;YAClB,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;SAC5B,CAAC,CAAC;QAEH,OAAO;YACL,mBAAmB,EAAE,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC;YAC5D,aAAa,EAAE,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;YAChD,kBAAkB,EAAE,MAAM,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC;YACjE,gBAAgB,EAAE,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC;SACzD,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,OAAe;QAC3C,MAAM,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACzD,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;YACzC,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;YAClC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC;SAClC,CAAC,CAAC;QAEH,OAAO;YACL,aAAa,EAAE,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,QAAQ,CAAC;YAC5D,UAAU,EAAE,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YAC5C,eAAe,EAAE,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,CAAC;SAC9E,CAAC;IACJ,CAAC;IAEO,wBAAwB,CAAC,UAAuB;QACtD,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QACtC,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC;IACtF,CAAC;IAEO,oBAAoB,CAAC,UAAuB;QAClD,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QACtC,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC;IAC1G,CAAC;IAEO,wBAAwB,CAAC,QAAuB;QACtD,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,EAAE,YAAY,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC,EAAE,CAAC;QAExE,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,GAAG,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;QACjG,OAAO;YACL,YAAY;YACZ,aAAa,EAAE,QAAQ,CAAC,MAAM;YAC9B,WAAW,EAAE,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC;SACjD,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,OAAe;QAChD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;YAClD,KAAK,EAAE,EAAE,OAAO,EAAE;YAClB,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;SAC7B,CAAC,CAAC;QAEH,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,WAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEjC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAC5C,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAE/B,IAAI,SAAS,CAAC,OAAO,EAAE,KAAK,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;gBAClD,SAAS;YACX,CAAC;YAED,IAAI,SAAS,CAAC,OAAO,EAAE,KAAK,WAAW,CAAC,OAAO,EAAE,GAAG,QAAQ,EAAE,CAAC;gBAC7D,MAAM,EAAE,CAAC;gBACT,WAAW,GAAG,SAAS,CAAC;YAC1B,CAAC;iBAAM,CAAC;gBACN,MAAM;YACR,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,OAAe;QAC3C,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAC5C,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;YACzC,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;SACnC,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,CAAC,WAAW;YACnB,OAAO,CAAC,eAAe;YACvB,QAAQ,CAAC,gBAAgB;YACzB,OAAO,CAAC,eAAe,CAAC,YAAY;YACpC,OAAO,CAAC,WAAW;SAEpB,CAAC;IACJ,CAAC;IAEO,mBAAmB,CAAC,QAAkB;QAE5C,OAAO,GAAG,CAAC;IACb,CAAC;IAEO,6BAA6B,CAAC,QAAkB;QAEtD,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,0BAA0B,CAAC,MAAoB;QAErD,OAAO;YACL,OAAO,EAAE,GAAG;YACZ,SAAS,EAAE,GAAG;YACd,OAAO,EAAE,GAAG;SACb,CAAC;IACJ,CAAC;IAEO,oBAAoB,CAAC,MAAoB;QAE/C,OAAO;YACL,eAAe,EAAE,EAAE;YACnB,cAAc,EAAE,GAAG;YACnB,eAAe,EAAE,EAAE;SACpB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,OAAe;QAErD,OAAO;YACL,OAAO,EAAE,IAAI;YACb,UAAU,EAAE,IAAI;YAChB,SAAS,EAAE,IAAI;SAChB,CAAC;IACJ,CAAC;IAEO,yBAAyB,CAAC,MAAoB;QAEpD,OAAO,GAAG,CAAC;IACb,CAAC;IAEO,qBAAqB,CAAC,OAAY,EAAE,QAAa;QAEvD,OAAO;YACL,gBAAgB,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;YAC7C,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE,OAAO;SACnB,CAAC;IACJ,CAAC;IAEO,kBAAkB,CAAC,OAAY;QAErC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IAChC,CAAC;IAEO,uBAAuB,CAAC,OAAY,EAAE,QAAa,EAAE,WAAgB;QAE3E,OAAO;YACL,qCAAqC;YACrC,4BAA4B;YAC5B,4BAA4B;SAC7B,CAAC;IACJ,CAAC;IAEO,oBAAoB,CAAC,QAAuB;QAClD,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,CAAC,CAAC;QAClC,MAAM,cAAc,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,MAAM,WAAW,GAAG,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAC9F,OAAO,WAAW,CAAC;IACrB,CAAC;CACF,CAAA;AAtSY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;IAMR,WAAA,IAAA,0BAAgB,EAAC,0BAAQ,CAAC,CAAA;IAE1B,WAAA,IAAA,0BAAgB,EAAC,mCAAY,CAAC,CAAA;IAE9B,WAAA,IAAA,0BAAgB,EAAC,+BAAU,CAAC,CAAA;IAE5B,WAAA,IAAA,0BAAgB,EAAC,4BAAS,CAAC,CAAA;IAE3B,WAAA,IAAA,0BAAgB,EAAC,iCAAW,CAAC,CAAA;yDAPF,oBAAU,oBAAV,oBAAU,oDAEN,oBAAU,oBAAV,oBAAU,oDAEZ,oBAAU,oBAAV,oBAAU,oDAEX,oBAAU,oBAAV,oBAAU,oDAER,oBAAU,oBAAV,oBAAU;GAdhC,gBAAgB,CAsS5B"}