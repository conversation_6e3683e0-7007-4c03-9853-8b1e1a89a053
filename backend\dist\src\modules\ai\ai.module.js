"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const ai_recommendation_service_1 = require("./ai-recommendation.service");
const ai_recommendation_controller_1 = require("./ai-recommendation.controller");
const user_entity_1 = require("../../entities/user.entity");
const materials_entity_1 = require("../../entities/materials.entity");
const cpd_tracking_entity_1 = require("../../entities/cpd-tracking.entity");
const learning_suggestions_entity_1 = require("../../entities/learning-suggestions.entity");
const ai_service_1 = require("./ai.service");
const redis_module_1 = require("../redis/redis.module");
let AIModule = class AIModule {
};
exports.AIModule = AIModule;
exports.AIModule = AIModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                user_entity_1.User,
                materials_entity_1.Material,
                cpd_tracking_entity_1.CPDActivity,
                learning_suggestions_entity_1.LearningSuggestion,
            ]),
            redis_module_1.RedisModule
        ],
        providers: [ai_recommendation_service_1.AIRecommendationService, ai_service_1.AIService],
        controllers: [ai_recommendation_controller_1.AIRecommendationController],
        exports: [ai_recommendation_service_1.AIRecommendationService],
    })
], AIModule);
//# sourceMappingURL=ai.module.js.map