{"version": 3, "file": "ai-features.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/features/ai-features.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,0DAAsD;AACtD,mEAAkF;AAuC3E,IAAM,iBAAiB,yBAAvB,MAAM,iBAAiB;IAM5B,YACmB,YAA0B,EAC1B,mBAAwC;QADxC,iBAAY,GAAZ,YAAY,CAAc;QAC1B,wBAAmB,GAAnB,mBAAmB,CAAqB;QAP1C,WAAM,GAAG,IAAI,eAAM,CAAC,mBAAiB,CAAC,IAAI,CAAC,CAAC;QAC5C,cAAS,GAAG,IAAI,CAAC;QACjB,4BAAuB,GAAG,mBAAmB,CAAC;QAC9C,uBAAkB,GAAG,WAAW,CAAC;IAK/C,CAAC;IAKJ,KAAK,CAAC,sBAAsB,CAAC,SAAiB,EAAE,WAAgB;QAC9D,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,GAAG,IAAI,CAAC,uBAAuB,GAAG,SAAS,EAAE,CAAC;YAC/D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAErD,IAAI,MAAM,EAAE,CAAC;gBACX,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAC5B,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;YAE5E,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;YAEpF,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0CAA0C,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YACjF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,eAAe,CACnB,MAAc,EACd,mBAA6B,EAC7B,OAA6B;QAE7B,IAAI,CAAC;YAEH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;YACjF,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,IAAI,KAAK,CAAC,wCAAwC,MAAM,EAAE,CAAC,CAAC;YACpE,CAAC;YAGD,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC,GAAG,CACvC,mBAAmB,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE;gBAE1C,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;gBACzD,OAAO,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;YAC7D,CAAC,CAAC,CACH,CAAC;YAGF,MAAM,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAEtE,OAAO;gBACL,MAAM;gBACN,YAAY;gBACZ,eAAe;gBACf,eAAe;gBACf,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;YAC5E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,mBAAmB,CACvB,OAAiB,EACjB,UAAoB,EACpB,OAA6B;QAQ7B,IAAI,CAAC;YAEH,MAAM,iBAAiB,GAAG,MAAM,OAAO,CAAC,GAAG,CACzC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;gBAC3B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;gBAC7E,OAAO,QAAQ,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3C,CAAC,CAAC,CACH,CAAC;YAGF,MAAM,oBAAoB,GAAG,MAAM,OAAO,CAAC,GAAG,CAC5C,UAAU,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE;gBACjC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;gBACzD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;gBAC3E,OAAO,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;YAChD,CAAC,CAAC,CACH,CAAC;YAGF,MAAM,oBAAoB,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAChD,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,OAAO,CAAC,CAC9C,CAAC;YAEF,OAAO;gBACL,iBAAiB;gBACjB,oBAAoB;gBACpB,oBAAoB;gBACpB,OAAO;gBACP,UAAU;aACX,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YAC7D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,sBAAsB,CAAC,KAA4B;QACvD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,GAAG,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YACtH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAErD,IAAI,MAAM,EAAE,CAAC;gBACX,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAC5B,CAAC;YAGD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;YAE5D,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAEnF,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,wBAAwB,CAC5B,MAAc,EACd,UAAoB;QAEpB,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YAI7D,MAAM,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;gBAC7D,WAAW,EAAE,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;gBAC3C,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;gBAC3B,QAAQ,EAAE,MAAe;aAC1B,CAAC,CAAC,CAAC;YAEJ,MAAM,eAAe,GAAG,KAAK,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAC9D,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;gBAC3D,WAAW,EAAE,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC;gBAC9C,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;gBAC3B,QAAQ,EAAE,SAAkB;aAC7B,CAAC,CAAC,CACJ,CAAC;YAEF,MAAM,eAAe,GAAG,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;gBACnE,WAAW,EAAE,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC;gBAC9C,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;gBAC3B,QAAQ,EAAE,SAAkB;aAC7B,CAAC,CAAC,CAAC;YAEJ,OAAO,CAAC,GAAG,YAAY,EAAE,GAAG,eAAe,EAAE,GAAG,eAAe,CAAC;iBAC7D,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAClE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,cAAc,CAAC,SAAiB;QAE5C,OAAO;YACL,EAAE,EAAE,SAAS;YACb,IAAI,EAAE,SAAS;YACf,UAAU,EAAE,GAAG;YACf,MAAM,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;YAC3B,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE;gBACR,MAAM,EAAE,SAAS;gBACjB,KAAK,EAAE,SAAS;aACjB;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,SAAiB,EAAE,WAAgB;QAEvE,OAAO;YACL,SAAS;YACT,WAAW,EAAE,WAAW,CAAC,IAAI;YAC7B,UAAU,EAAE,WAAW,CAAC,UAAU;YAClC,MAAM,EAAE,WAAW,CAAC,MAAM;YAC1B,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,UAAU,EAAE,GAAG;YACf,cAAc,EAAE,GAAG;YACnB,YAAY,EAAE,IAAI;YAClB,aAAa,EAAE,EAAE;YACjB,kBAAkB,EAAE,EAAE;YACtB,QAAQ,EAAE,WAAW,CAAC,QAAQ;SAC/B,CAAC;IACJ,CAAC;IAEO,uBAAuB,CAAC,MAAc,EAAE,OAA6B;QAE3E,OAAO;YACL,OAAO,EAAE,SAAS,IAAI,GAAG;YACzB,OAAO,EAAE,SAAS,IAAI,GAAG;YACzB,OAAO,EAAE,UAAU,IAAI,GAAG;YAC1B,OAAO,EAAE,QAAQ,IAAI,GAAG;SACzB,CAAC;IACJ,CAAC;IAEO,uBAAuB,CAAC,QAAyB;QACvD,OAAO;YACL,QAAQ,CAAC,UAAU;YACnB,QAAQ,CAAC,QAAQ,GAAG,IAAI;YACxB,QAAQ,CAAC,UAAU;YACnB,QAAQ,CAAC,cAAc;YACvB,QAAQ,CAAC,YAAY;YACrB,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG,EAAE;YAC3B,QAAQ,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC;YACjC,QAAQ,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC;SACvC,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,KAA4B;QAG9D,OAAO,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC3C,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE;YACpB,UAAU,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG;YACrC,SAAS,EAAE;gBACT,8BAA8B;gBAC9B,oCAAoC;gBACpC,4BAA4B;aAC7B;YACD,QAAQ,EAAE,OAAO,CAAC,WAAW;SAC9B,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,kBAAkB,CAAC,KAAa;QACtC,MAAM,KAAK,GAAG;YACZ,kBAAkB;YAClB,mBAAmB;YACnB,wBAAwB;YACxB,kBAAkB;YAClB,sBAAsB;YACtB,kBAAkB;YAClB,oBAAoB;YACpB,gBAAgB;YAChB,eAAe;YACf,iBAAiB;YACjB,gBAAgB;YAChB,aAAa;YACb,wBAAwB;YACxB,mBAAmB;YACnB,cAAc;YACd,sBAAsB;YACtB,sBAAsB;SACvB,CAAC;QACF,OAAO,KAAK,CAAC,KAAK,CAAC,IAAI,gBAAgB,KAAK,EAAE,CAAC;IACjD,CAAC;IAEO,qBAAqB,CAAC,KAAa;QACzC,MAAM,KAAK,GAAG;YACZ,YAAY;YACZ,UAAU;YACV,YAAY;YACZ,iBAAiB;YACjB,eAAe;YACf,aAAa;YACb,oBAAoB;YACpB,0BAA0B;SAC3B,CAAC;QACF,OAAO,KAAK,CAAC,KAAK,CAAC,IAAI,mBAAmB,KAAK,EAAE,CAAC;IACpD,CAAC;IAEO,qBAAqB,CAAC,KAAa;QACzC,MAAM,KAAK,GAAG;YACZ,aAAa;YACb,aAAa;YACb,aAAa;YACb,UAAU;SACX,CAAC;QACF,OAAO,KAAK,CAAC,KAAK,CAAC,IAAI,mBAAmB,KAAK,EAAE,CAAC;IACpD,CAAC;CACF,CAAA;AAlTY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;qCAQsB,4BAAY;QACL,2CAAmB;GARhD,iBAAiB,CAkT7B"}