import { Request } from 'express';
import { UnitQuizService } from './unit-quiz.service';
export declare class UnitQuizController {
    private readonly unitQuizService;
    constructor(unitQuizService: UnitQuizService);
    getQuiz(unitId: string): Promise<{
        title: string;
        instructions: string;
        questions: {
            id: string;
            text: string;
            options: string[];
        }[];
    }>;
    getEligibility(unitId: string, req: Request): Promise<{
        eligible: boolean;
    }>;
    submitQuiz(unitId: string, req: Request, body: {
        answers: Record<string, string>;
    }): Promise<{
        score: number;
        passed: boolean;
        feedback: string;
    }>;
}
