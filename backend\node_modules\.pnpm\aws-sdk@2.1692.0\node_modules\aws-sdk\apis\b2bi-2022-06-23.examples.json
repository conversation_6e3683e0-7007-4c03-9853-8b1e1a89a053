{"version": "1.0", "examples": {"CreateCapability": [{"input": {"name": "b2<PERSON><PERSON><PERSON>", "type": "edi", "clientToken": "foo", "configuration": {"edi": {"type": {"x12Details": {"version": "VERSION_4010", "transactionSet": "X12_110"}}, "inputLocation": {"key": "input/", "bucketName": "test-bucket"}, "outputLocation": {"key": "output/", "bucketName": "test-bucket"}, "transformerId": "tr-9a893cf536df4658b"}}, "instructionsDocuments": [{"key": "instructiondoc.txt", "bucketName": "test-bucket"}], "tags": [{"Key": "capabilityKey1", "Value": "capabilityValue1"}]}, "output": {"name": "b2<PERSON><PERSON><PERSON>", "type": "edi", "capabilityArn": "arn:aws:b2bi:us-west-2:123456789012:capability/ca-963a8121e4fc4e348", "capabilityId": "ca-963a8121e4fc4e348", "configuration": {"edi": {"type": {"x12Details": {"version": "VERSION_4010", "transactionSet": "X12_110"}}, "inputLocation": {"key": "input/", "bucketName": "test-bucket"}, "outputLocation": {"key": "output/", "bucketName": "test-bucket"}, "transformerId": "tr-9a893cf536df4658b"}}, "createdAt": "2023-11-01T21:51:05.504Z", "instructionsDocuments": [{"key": "instructiondoc.txt", "bucketName": "test-bucket"}]}, "id": "example-1", "title": "Sample CreateCapability call"}], "CreatePartnership": [{"input": {"name": "b2bipartner", "capabilities": ["ca-963a8121e4fc4e348"], "clientToken": "foo", "email": "<EMAIL>", "phone": "5555555555", "profileId": "p-60fbc37c87f04fce9", "tags": [{"Key": "sampleKey1", "Value": "sampleValue1"}]}, "output": {"name": "b2bipartner", "capabilities": ["ca-963a8121e4fc4e348"], "createdAt": "2023-11-01T21:51:05.504Z", "email": "<EMAIL>", "partnershipArn": "arn:aws:b2bi:us-west-2:123456789012:partnership/ps-60fbc37c87f04fce9", "partnershipId": "ps-219fa02f5b4242af8", "phone": "5555555555", "profileId": "p-60fbc37c87f04fce9", "tradingPartnerId": "tp-2a17ca447f6f4a8a8"}, "id": "example-1", "title": "Sample CreatePartnership call"}], "CreateProfile": [{"input": {"name": "Shipping Profile", "businessName": "John's Shipping", "clientToken": "foo", "email": "<EMAIL>", "logging": "ENABLED", "phone": "5555555555", "tags": [{"Key": "sampleKey", "Value": "sampleValue"}]}, "output": {"name": "Shipping Profile", "businessName": "<PERSON>'s Trucking", "createdAt": "2023-11-01T21:51:05.504Z", "email": "<EMAIL>", "logGroupName": "b2bi/p-60fbc37c87f04fce9-Logs", "logging": "ENABLED", "phone": "5555555555", "profileArn": "arn:aws:b2bi:us-west-2:123456789012:profile/p-60fbc37c87f04fce9", "profileId": "p-60fbc37c87f04fce9"}, "id": "example-1", "title": "Sample CreateProfile call"}], "CreateTransformer": [{"input": {"name": "transformJSON", "clientToken": "foo", "ediType": {"x12Details": {"version": "VERSION_4010", "transactionSet": "X12_110"}}, "fileFormat": "JSON", "mappingTemplate": "{}", "sampleDocument": "s3://test-bucket/sampleDoc.txt", "tags": [{"Key": "sampleKey", "Value": "sampleValue"}]}, "output": {"name": "transformJSON", "createdAt": "2023-11-01T21:51:05.504Z", "ediType": {"x12Details": {"version": "VERSION_4010", "transactionSet": "X12_110"}}, "fileFormat": "JSON", "mappingTemplate": "$", "sampleDocument": "s3://test-bucket/sampleDoc.txt", "status": "inactive", "transformerArn": "arn:aws:b2bi:us-west-2:123456789012:transformer/tr-974c129999f84d8c9", "transformerId": "tr-974c129999f84d8c9"}, "id": "example-1", "title": "Sample CreateTransformer call"}], "DeleteCapability": [{"input": {"capabilityId": "ca-963a8121e4fc4e348"}, "id": "example-1", "title": "Sample DeleteCapabilty call"}], "DeletePartnership": [{"input": {"partnershipId": "ps-219fa02f5b4242af8"}, "id": "example-1", "title": "Sample DeletePartnership call"}], "DeleteProfile": [{"input": {"profileId": "p-60fbc37c87f04fce9"}, "id": "example-1", "title": "Sample DeleteProfile call"}], "DeleteTransformer": [{"input": {"transformerId": "tr-974c129999f84d8c9"}, "id": "example-1", "title": "Sample DeleteTransformer call"}], "GetCapability": [{"input": {"capabilityId": "ca-963a8121e4fc4e348"}, "output": {"name": "b2<PERSON><PERSON><PERSON>", "type": "edi", "capabilityArn": "arn:aws:b2bi:us-west-2:123456789012:capability/ca-963a8121e4fc4e348", "capabilityId": "ca-963a8121e4fc4e348", "configuration": {"edi": {"type": {"x12Details": {"version": "VERSION_4010", "transactionSet": "X12_110"}}, "inputLocation": {"key": "input/", "bucketName": "test-bucket"}, "outputLocation": {"key": "output/", "bucketName": "test-bucket"}, "transformerId": "tr-9a893cf536df4658b"}}, "createdAt": "2023-11-01T21:51:05.504Z", "instructionsDocuments": [{"key": "instructiondoc.txt", "bucketName": "test-bucket"}], "modifiedAt": "2023-11-02T21:51:05.504Z"}, "id": "example-1", "title": "Sample GetCapabilty call"}], "GetPartnership": [{"input": {"partnershipId": "ps-219fa02f5b4242af8"}, "output": {"name": "b2bipartner", "capabilities": ["ca-963a8121e4fc4e348"], "createdAt": "2023-11-01T21:51:05.504Z", "email": "<EMAIL>", "modifiedAt": "2023-11-01T21:51:05.504Z", "partnershipArn": "arn:aws:b2bi:us-west-2:123456789012:partnership/ps-219fa02f5b4242af8", "partnershipId": "ps-219fa02f5b4242af8", "phone": "5555555555", "profileId": "p-60fbc37c87f04fce9", "tradingPartnerId": "tp-2a17ca447f6f4a8a8"}, "id": "example-1", "title": "Sample GetPartnership call"}], "GetProfile": [{"input": {"profileId": "p-60fbc37c87f04fce9"}, "output": {"name": "Shipping Profile", "businessName": "<PERSON>'s Trucking", "createdAt": "2023-11-01T21:51:05.504Z", "email": "<EMAIL>", "logGroupName": "b2bi/p-60fbc37c87f04fce9-Logs", "logging": "ENABLED", "phone": "5555555555", "profileArn": "arn:aws:b2bi:us-west-2:123456789012:profile/p-60fbc37c87f04fce9", "profileId": "p-60fbc37c87f04fce9"}, "id": "example-1", "title": "Sample GetProfile call"}], "GetTransformer": [{"input": {"transformerId": "tr-974c129999f84d8c9"}, "output": {"name": "transformJSON", "createdAt": "2023-11-01T21:51:05.504Z", "ediType": {"x12Details": {"version": "VERSION_4010", "transactionSet": "X12_110"}}, "fileFormat": "JSON", "mappingTemplate": "$", "modifiedAt": "2023-11-01T21:51:05.504Z", "sampleDocument": "s3://test-bucket/sampleDoc.txt", "status": "inactive", "transformerArn": "arn:aws:b2bi:us-west-2:123456789012:transformer/tr-974c129999f84d8c9", "transformerId": "tr-974c129999f84d8c9"}, "id": "example-1", "title": "<PERSON>ple GetTransformer call"}], "GetTransformerJob": [{"input": {"transformerId": "tr-974c129999f84d8c9", "transformerJobId": "tj-vpYxfV7yQOqjMSYllEslLw"}, "output": {"message": "Transformed, writing output", "outputFiles": [{"key": "output/sample-214.edi.2023-11-01T10:44:03.353Z.json", "bucketName": "gt-edi-test"}], "status": "succeeded"}, "id": "example-1", "title": "<PERSON><PERSON> GetTransformer<PERSON><PERSON> call"}], "ListCapabilities": [{"input": {"maxResults": 50, "nextToken": "foo"}, "output": {"capabilities": [{"name": "b2<PERSON><PERSON><PERSON>", "type": "edi", "capabilityId": "ca-963a8121e4fc4e348", "createdAt": "2023-11-01T21:51:05.504Z", "modifiedAt": "2023-11-01T21:51:05.504Z"}], "nextToken": "foo"}, "id": "example-1", "title": "Sample ListCapabilities call"}], "ListPartnerships": [{"input": {"maxResults": 50, "nextToken": "foo", "profileId": "p-60fbc37c87f04fce9"}, "output": {"nextToken": "string", "partnerships": [{"name": "b2bipartner", "capabilities": ["ca-963a8121e4fc4e348"], "createdAt": "2023-11-01T21:51:05.504Z", "modifiedAt": "2023-11-01T21:51:05.504Z", "partnershipId": "ps-219fa02f5b4242af8", "profileId": "p-60fbc37c87f04fce9", "tradingPartnerId": "tp-2a17ca447f6f4a8a8"}]}, "id": "example-1", "title": "Sample ListPartnerships call"}], "ListProfiles": [{"input": {"maxResults": 50, "nextToken": "foo"}, "output": {"nextToken": "foo", "profiles": [{"name": "Shipping Profile", "businessName": "<PERSON>'s Trucking", "createdAt": "2023-11-01T21:51:05.504Z", "logGroupName": "b2bi/p-60fbc37c87f04fce9-Logs", "logging": "ENABLED", "profileId": "p-60fbc37c87f04fce9"}]}, "id": "example-1", "title": "Sample ListProfiles call"}], "ListTagsForResource": [{"input": {"ResourceARN": "arn:aws:b2bi:us-west-2:123456789012:profile/p-60fbc37c87f04fce9"}, "output": {"Tags": [{"Key": "sampleKey", "Value": "SampleValue"}]}, "id": "example-1", "title": "Sample ListTagsForResources call"}], "ListTransformers": [{"input": {"maxResults": 50, "nextToken": "foo"}, "output": {"nextToken": "foo", "transformers": [{"name": "transformJSON", "createdAt": "2023-11-01T21:51:05.504Z", "ediType": {"x12Details": {"version": "VERSION_4010", "transactionSet": "X12_110"}}, "fileFormat": "JSON", "mappingTemplate": "$", "modifiedAt": "2023-11-01T21:51:05.504Z", "sampleDocument": "s3://test-bucket/sampleDoc.txt", "status": "inactive", "transformerId": "tr-974c129999f84d8c9"}]}, "id": "example-1", "title": "Sample ListTransformers call"}], "StartTransformerJob": [{"input": {"clientToken": "foo", "inputFile": {"key": "input/inputFile.txt", "bucketName": "test-bucket"}, "outputLocation": {"key": "output/", "bucketName": "test-bucket"}, "transformerId": "tr-974c129999f84d8c9"}, "output": {"transformerJobId": "tj-vpYxfV7yQOqjMSYllEslLw"}, "id": "example-1", "title": "Sam<PERSON> StartTransformer<PERSON><PERSON> call"}], "TagResource": [{"input": {"ResourceARN": "arn:aws:b2bi:us-west-2:123456789012:profile/p-60fbc37c87f04fce9", "Tags": [{"Key": "sampleKey", "Value": "SampleValue"}]}, "id": "example-1", "title": "Sample TagResource call"}], "TestMapping": [{"input": {"fileFormat": "JSON", "inputFileContent": "Sample file content", "mappingTemplate": "$"}, "output": {"mappedFileContent": "Sample file content"}, "id": "example-1", "title": "<PERSON>ple TestMapping call"}], "TestParsing": [{"input": {"ediType": {"x12Details": {"version": "VERSION_4010", "transactionSet": "X12_110"}}, "fileFormat": "JSON", "inputFile": {"key": "sampleFile.txt", "bucketName": "test-bucket"}}, "output": {"parsedFileContent": "Sample parsed file content"}, "id": "example-1", "title": "Sample TestParsing call"}], "UntagResource": [{"input": {"ResourceARN": "arn:aws:b2bi:us-west-2:123456789012:profile/p-60fbc37c87f04fce9", "TagKeys": ["sampleKey"]}, "id": "example-1", "title": "Sample UntagResource call"}], "UpdateCapability": [{"input": {"name": "b2<PERSON><PERSON><PERSON>", "capabilityId": "ca-963a8121e4fc4e348", "configuration": {"edi": {"type": {"x12Details": {"version": "VERSION_4010", "transactionSet": "X12_110"}}, "inputLocation": {"key": "input/", "bucketName": "test-bucket"}, "outputLocation": {"key": "output/", "bucketName": "test-bucket"}, "transformerId": "tr-9a893cf536df4658b"}}, "instructionsDocuments": [{"key": "instructiondoc.txt", "bucketName": "test-bucket"}]}, "output": {"name": "b2<PERSON><PERSON><PERSON>", "type": "edi", "capabilityArn": "arn:aws:b2bi:us-west-2:123456789012:capability/ca-963a8121e4fc4e348", "capabilityId": "ca-963a8121e4fc4e348", "configuration": {"edi": {"type": {"x12Details": {"version": "VERSION_4010", "transactionSet": "X12_110"}}, "inputLocation": {"key": "input/", "bucketName": "test-bucket"}, "outputLocation": {"key": "output/", "bucketName": "test-bucket"}, "transformerId": "tr-9a893cf536df4658b"}}, "createdAt": "2023-11-01T21:51:05.504Z", "instructionsDocuments": [{"key": "instructiondoc.txt", "bucketName": "test-bucket"}], "modifiedAt": "2023-11-01T21:51:05.504Z"}, "id": "example-1", "title": "Sample UpdateCapability call"}], "UpdatePartnership": [{"input": {"name": "b2bipartner", "capabilities": ["ca-963a8121e4fc4e348"], "partnershipId": "ps-219fa02f5b4242af8"}, "output": {"name": "b2bipartner", "capabilities": ["ca-963a8121e4fc4e348"], "createdAt": "2023-11-01T21:51:05.504Z", "email": "<EMAIL>", "modifiedAt": "2023-11-01T21:51:05.504Z", "partnershipArn": "arn:aws:b2bi:us-west-2:123456789012:partnership/ps-60fbc37c87f04fce9", "partnershipId": "ps-219fa02f5b4242af8", "phone": "5555555555", "profileId": "p-60fbc37c87f04fce9", "tradingPartnerId": "tp-2a17ca447f6f4a8a8"}, "id": "example-1", "title": "Sample UpdatePartnership call"}], "UpdateProfile": [{"input": {"name": "Shipping Profile", "businessName": "John's Shipping", "email": "<EMAIL>", "phone": "5555555555", "profileId": "p-60fbc37c87f04fce9"}, "output": {"name": "Shipping Profile", "businessName": "<PERSON>'s Trucking", "createdAt": "2023-11-01T21:51:05.504Z", "email": "<EMAIL>", "logGroupName": "b2bi/p-60fbc37c87f04fce9-Logs", "logging": "ENABLED", "modifiedAt": "2023-11-02T21:51:05.504Z", "phone": "5555555555", "profileArn": "arn:aws:b2bi:us-west-2:123456789012:profile/p-60fbc37c87f04fce9", "profileId": "p-60fbc37c87f04fce9"}, "id": "example-1", "title": "Sample UpdateProfile call"}], "UpdateTransformer": [{"input": {"name": "transformJSON", "ediType": {"x12Details": {"version": "VERSION_4010", "transactionSet": "X12_110"}}, "fileFormat": "JSON", "mappingTemplate": "{}", "sampleDocument": "s3://test-bucket/sampleDoc.txt", "status": "inactive", "transformerId": "tr-974c129999f84d8c9"}, "output": {"name": "transformJSON", "createdAt": "2023-11-01T21:51:05.504Z", "ediType": {"x12Details": {"version": "VERSION_4010", "transactionSet": "X12_110"}}, "fileFormat": "JSON", "mappingTemplate": "$", "modifiedAt": "2023-11-01T21:51:05.504Z", "sampleDocument": "s3://test-bucket/sampleDoc.txt", "status": "inactive", "transformerArn": "arn:aws:b2bi:us-west-2:607686414464:transformer/tr-974c129999f84d8c9", "transformerId": "tr-974c129999f84d8c9"}, "id": "example-1", "title": "Sample UpdateTransformer call"}]}}