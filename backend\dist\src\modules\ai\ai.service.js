"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AIService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const model_1 = require("./model");
const materials_entity_1 = require("../../entities/materials.entity");
const learning_suggestion_entity_1 = require("../../entities/learning-suggestion.entity");
const redis_service_1 = require("../redis/redis.service");
const path_1 = require("path");
const user_entity_1 = require("../../entities/user.entity");
let AIService = AIService_1 = class AIService {
    constructor(userRepository, materialRepository, suggestionRepository, redisService) {
        this.userRepository = userRepository;
        this.materialRepository = materialRepository;
        this.suggestionRepository = suggestionRepository;
        this.redisService = redisService;
        this.logger = new common_1.Logger(AIService_1.name);
        this.cacheTTL = 3600;
        this.modelPath = (0, path_1.join)(process.cwd(), 'models', 'recommendation-model');
        this.initializeModel();
    }
    async initializeModel() {
        this.model = new model_1.AIModel(this.modelPath);
        try {
            await this.model.load();
            this.logger.log('Loaded existing model');
        }
        catch (error) {
            this.logger.log('No existing model found, initializing new model');
            await this.model.initialize();
        }
    }
    async getRecommendations(userId) {
        try {
            const cacheKey = `recommendations:${userId}`;
            const cachedRecommendations = await this.redisService.get(cacheKey);
            if (cachedRecommendations) {
                return JSON.parse(cachedRecommendations);
            }
            const userFeatures = await this.extractUserFeatures(userId);
            const score = await this.model.predict(this.featuresToArray(userFeatures));
            const materials = await this.getRelevantMaterials(userFeatures);
            const recommendations = materials.map((material) => ({
                materialId: material.id,
                score: score * this.calculateMaterialRelevance(material, userFeatures),
                reason: this.generateRecommendationReason(material, userFeatures),
            }));
            const topRecommendations = recommendations
                .sort((a, b) => b.score - a.score)
                .slice(0, 5);
            await this.redisService.set(cacheKey, JSON.stringify(topRecommendations), this.cacheTTL);
            return topRecommendations;
        }
        catch (error) {
            this.logger.error('Error generating recommendations:', error);
            return [];
        }
    }
    async extractUserFeatures(userId) {
        const user = await this.userRepository.findOne({
            where: { id: userId },
            relations: ['learningHistory', 'preferences'],
        });
        if (!user) {
            throw new Error('User not found');
        }
        const learningHistory = (user.learningHistory || []).map(item => ({
            ...item,
            timestamp: item.timestamp instanceof Date ? item.timestamp.toISOString() : item.timestamp
        }));
        const preferences = user.preferences || { learningStyle: 0.5 };
        return {
            completedMaterials: learningHistory.length,
            averageScore: this.calculateAverageScore(learningHistory),
            studyTime: this.calculateTotalStudyTime(learningHistory),
            preferredCategories: this.extractPreferredCategories(learningHistory),
            lastActivity: this.getLastActivityDate(learningHistory),
            difficultyPreference: this.calculateDifficultyPreference(learningHistory),
            learningStyle: this.determineLearningStyle(preferences),
            engagementLevel: this.calculateEngagementLevel(learningHistory),
            quizPerformance: this.calculateQuizPerformance(learningHistory),
            materialInteraction: this.calculateMaterialInteraction(learningHistory),
        };
    }
    featuresToArray(features) {
        return [
            features.completedMaterials,
            features.averageScore,
            features.studyTime,
            features.difficultyPreference,
            features.learningStyle,
            features.engagementLevel,
            features.quizPerformance,
            features.materialInteraction,
            this.calculateCategoryScore(features.preferredCategories),
            this.calculateActivityScore(features.lastActivity),
        ];
    }
    async getRelevantMaterials(features) {
        return this.materialRepository.find({
            where: {
                category: (0, typeorm_2.In)(features.preferredCategories),
                difficulty: features.difficultyPreference,
            },
            relations: ['author', 'unit'],
        });
    }
    calculateMaterialRelevance(material, features) {
        return 1.0;
    }
    generateRecommendationReason(material, features) {
        return `Based on your learning style and performance in ${material.category}`;
    }
    calculateAverageScore(history) {
        if (!history?.length)
            return 0;
        return history.reduce((sum, item) => sum + (item.score || 0), 0) / history.length;
    }
    calculateTotalStudyTime(history) {
        if (!history?.length)
            return 0;
        return history.reduce((sum, item) => sum + (item.duration || 0), 0);
    }
    extractPreferredCategories(history) {
        if (!history?.length)
            return [];
        const categories = history.map((item) => item.category);
        return [...new Set(categories)];
    }
    getLastActivityDate(history) {
        if (!history?.length)
            return new Date(0);
        return new Date(Math.max(...history.map((item) => new Date(item.timestamp).getTime())));
    }
    calculateDifficultyPreference(history) {
        if (!history?.length)
            return 0.5;
        return history.reduce((sum, item) => sum + (item.difficulty || 0.5), 0) / history.length;
    }
    determineLearningStyle(preferences) {
        return preferences?.learningStyle || 0.5;
    }
    calculateEngagementLevel(history) {
        if (!history?.length)
            return 0;
        return history.reduce((sum, item) => sum + (item.engagement || 0), 0) / history.length;
    }
    calculateQuizPerformance(history) {
        if (!history?.length)
            return 0;
        const quizHistory = history.filter((item) => item.type === 'quiz');
        return quizHistory.length
            ? quizHistory.reduce((sum, item) => sum + (item.score || 0), 0) / quizHistory.length
            : 0;
    }
    calculateMaterialInteraction(history) {
        if (!history?.length)
            return 0;
        return history.reduce((sum, item) => sum + (item.interactionScore || 0), 0) / history.length;
    }
    calculateCategoryScore(categories) {
        return categories.length / 10;
    }
    calculateActivityScore(lastActivity) {
        const daysSinceLastActivity = (Date.now() - lastActivity.getTime()) / (1000 * 60 * 60 * 24);
        return Math.max(0, 1 - daysSinceLastActivity / 30);
    }
    async trainModel(trainingData) {
        try {
            await this.model.train(trainingData.inputs, trainingData.labels);
            await this.saveModel();
            this.logger.log('Model trained and saved successfully');
        }
        catch (error) {
            this.logger.error('Error training model:', error);
            throw error;
        }
    }
    async saveModel() {
        try {
            await this.model.save();
        }
        catch (error) {
            this.logger.error('Failed to save model:', error);
            throw error;
        }
    }
};
exports.AIService = AIService;
exports.AIService = AIService = AIService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(materials_entity_1.Material)),
    __param(2, (0, typeorm_1.InjectRepository)(learning_suggestion_entity_1.LearningSuggestion)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        redis_service_1.RedisService])
], AIService);
//# sourceMappingURL=ai.service.js.map