"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailVerificationGuard = void 0;
const common_1 = require("@nestjs/common");
const security_service_1 = require("../../modules/auth/security.service");
let EmailVerificationGuard = class EmailVerificationGuard {
    constructor(securityService) {
        this.securityService = securityService;
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        if (!user) {
            throw new common_1.UnauthorizedException('User not authenticated');
        }
        const isVerified = await this.securityService.isEmailVerified(user.id);
        if (!isVerified) {
            throw new common_1.UnauthorizedException('Email verification required');
        }
        return true;
    }
};
exports.EmailVerificationGuard = EmailVerificationGuard;
exports.EmailVerificationGuard = EmailVerificationGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [security_service_1.SecurityService])
], EmailVerificationGuard);
//# sourceMappingURL=email-verification.guard.js.map