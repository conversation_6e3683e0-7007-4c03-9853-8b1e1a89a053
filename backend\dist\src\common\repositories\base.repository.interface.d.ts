import { ObjectLiteral } from 'typeorm';
export interface IBaseRepository<T extends ObjectLiteral> {
    findAll(): Promise<T[]>;
    findById(id: string): Promise<T | null>;
    findOne(options: any): Promise<T | null>;
    findMany(filter: Partial<T>): Promise<T[]>;
    create(data: Partial<T>): Promise<T>;
    update(id: string, data: Partial<T>): Promise<T | null>;
    delete(id: string): Promise<boolean>;
    save(entity: T): Promise<T>;
}
import { Repository } from 'typeorm';
export declare class BaseRepository<T extends ObjectLiteral> implements IBaseRepository<T> {
    private readonly repository;
    constructor(repository: Repository<T>);
    findAll(): Promise<T[]>;
    findById(id: string): Promise<T | null>;
    findOne(filter: Partial<T>): Promise<T | null>;
    findMany(filter: Partial<T>): Promise<T[]>;
    create(data: Partial<T>): Promise<T>;
    update(id: string, data: Partial<T>): Promise<T | null>;
    delete(id: string): Promise<boolean>;
    save(entity: T): Promise<T>;
}
