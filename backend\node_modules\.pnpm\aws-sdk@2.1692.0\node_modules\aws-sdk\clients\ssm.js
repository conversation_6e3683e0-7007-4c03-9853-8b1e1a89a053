require('../lib/node_loader');
var AWS = require('../lib/core');
var Service = AWS.Service;
var apiLoader = AWS.apiLoader;

apiLoader.services['ssm'] = {};
AWS.SSM = Service.defineService('ssm', ['2014-11-06']);
Object.defineProperty(apiLoader.services['ssm'], '2014-11-06', {
  get: function get() {
    var model = require('../apis/ssm-2014-11-06.min.json');
    model.paginators = require('../apis/ssm-2014-11-06.paginators.json').pagination;
    model.waiters = require('../apis/ssm-2014-11-06.waiters2.json').waiters;
    return model;
  },
  enumerable: true,
  configurable: true
});

module.exports = AWS.SSM;
