import { Progress } from './progress.entity';
import { Material } from './materials.entity';
import { UserResponse } from './user-response.entity';
import { MaterialShare } from './material_shares.entity';
import { Feedback } from './feedback.entity';
import { Notification } from './notifications.entity';
import { ClinicalCase } from './clinical-case.entity';
import { StudySession } from './study-session.entity';
import { TopicProgress } from './topic-progress.entity';
import { Flashcard } from './flashcard.entity';
import { Role } from './role.entity';
import { CPDCycle } from './cpd-tracking.entity';
import { CPDActivity } from './cpd-activity.entity';
import { WeeklyDigest } from './weekly-digest.entity';
import { LearningSuggestion } from './learning-suggestion.entity';
export declare enum UserRole {
    STUDENT = "student",
    TEACHER = "teacher",
    ADMIN = "admin"
}
export declare class User {
    id: string;
    email: string;
    username: string;
    is_active: boolean;
    password_reset_token: string;
    password_reset_expires: Date;
    email_verification_token: string;
    email_verification_expires: Date;
    email_verified: boolean;
    name: string;
    password_hash: string;
    first_name: string;
    last_name: string;
    profile_picture: string;
    bio: string;
    phone_number: string;
    role: UserRole;
    is_locked: boolean;
    locked_until: Date;
    failed_login_attempts: number;
    progress: Progress[];
    quiz_responses: UserResponse[];
    created_materials: Material[];
    shared_materials: MaterialShare[];
    feedback: Feedback[];
    notifications: Notification[];
    created_clinical_cases: ClinicalCase[];
    createdAt: Date;
    updatedAt: Date;
    streak_days: number;
    study_sessions: StudySession[];
    topic_progress: TopicProgress[];
    flashcards: Flashcard[];
    roles: Role[];
    cpd_cycles: CPDCycle[];
    cpd_activities: CPDActivity[];
    weekly_digests: WeeklyDigest[];
    learning_suggestions: LearningSuggestion[];
    learningHistory: {
        timestamp: Date;
        type: string;
        score?: number;
        duration?: number;
        category?: string;
        difficulty?: number;
        engagement?: number;
        interactionScore?: number;
    }[];
    preferences: {
        learningStyle: number;
        preferredCategories: string[];
        difficultyPreference: number;
    };
}
export interface IUser {
    id: string;
    username: string;
    email: string;
    role: string;
    created_at: Date;
    updated_at: Date;
    password: string;
    password_hash: string;
    password_salt: string;
    password_reset_token: string;
    password_reset_expires: Date;
    email_verification_token: string;
    email_verification_expires: Date;
    email_verified: boolean;
}
export interface ICreateUser {
    id: string;
    username: string;
    email: string;
    role: string;
    created_at: Date;
    updated_at: Date;
    password: string;
    password_hash: string;
    password_salt: string;
    password_reset_token: string;
    password_reset_expires: Date;
    email_verification_token: string;
    email_verification_expires: Date;
    email_verified: boolean;
}
