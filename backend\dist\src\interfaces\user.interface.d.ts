export interface IUser {
    id: string;
    username: string;
    email: string;
    role: string;
    created_at: Date;
    updated_at: Date;
    password: string;
    password_hash: string;
    passwordSalt: string;
    passwordResetToken: string;
    passwordResetExpires: Date;
    emailVerificationToken: string;
    emailVerificationExpires: Date;
    emailVerified: boolean;
}
export interface UserInterface {
    id: string;
    email: string;
    name: string;
    password_hash: string;
    role: string;
    created_at: Date;
    updated_at: Date;
}
export interface UserCreateInterface {
    email: string;
    name: string;
    password: string;
}
export interface UserUpdateInterface {
    id: string;
    email?: string;
    name?: string;
    password?: string;
    role?: string;
    created_at?: Date;
    updated_at?: Date;
}
