"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SecurityService_1;
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const config_1 = require("@nestjs/config");
const bcrypt = __importStar(require("bcryptjs"));
const speakeasy = __importStar(require("speakeasy"));
const qrcode = __importStar(require("qrcode"));
const crypto = __importStar(require("crypto"));
const security_entity_1 = require("../../entities/security.entity");
const user_entity_1 = require("../../entities/user.entity");
const token_blacklist_service_1 = require("./token-blacklist.service");
let SecurityService = SecurityService_1 = class SecurityService {
    constructor(securitySettingsRepo, sessionRepo, securityEventRepo, userRepo, configService, tokenBlacklistService) {
        this.securitySettingsRepo = securitySettingsRepo;
        this.sessionRepo = sessionRepo;
        this.securityEventRepo = securityEventRepo;
        this.userRepo = userRepo;
        this.configService = configService;
        this.tokenBlacklistService = tokenBlacklistService;
        this.logger = new common_1.Logger(SecurityService_1.name);
    }
    generateBackupCodes(count = 10) {
        const codes = [];
        for (let i = 0; i < count; i++) {
            codes.push(Math.random().toString(36).substr(2, 10).toUpperCase());
        }
        return codes;
    }
    async setupTwoFactor(userId) {
        const user = await this.userRepo.findOne({ where: { id: userId } });
        if (!user) {
            throw new common_1.UnauthorizedException('User not found');
        }
        const secret = speakeasy.generateSecret({
            name: `MedicalApp:${user.email}`,
            issuer: 'MedicalApp',
        });
        let settings = await this.securitySettingsRepo.findOne({
            where: { userId },
        });
        const settingsData = {
            userId,
            twoFactorSecret: secret.base32,
            backupCodes: this.generateBackupCodes(),
        };
        if (!settings) {
            const newSettings = this.securitySettingsRepo.create(settingsData);
            settings = await this.securitySettingsRepo.save(newSettings);
        }
        else {
            Object.assign(settings, settingsData);
            settings = await this.securitySettingsRepo.save(settings);
        }
        const otpauthUrl = speakeasy.otpauthURL({
            secret: secret.ascii,
            label: `MedicalApp:${user.email}`,
            issuer: 'MedicalApp',
            encoding: 'base32',
        });
        const qrCodeData = await qrcode.toDataURL(otpauthUrl);
        return {
            secret: secret.base32,
            qrCode: qrCodeData,
        };
    }
    async verifyTwoFactor(userId, token) {
        const settings = await this.securitySettingsRepo.findOne({
            where: { userId },
        });
        if (!settings?.twoFactorSecret) {
            throw new common_1.BadRequestException('2FA not set up');
        }
        return speakeasy.totp.verify({
            secret: settings.twoFactorSecret,
            encoding: 'base32',
            token,
        });
    }
    async changePassword(userId, dto) {
        const user = await this.userRepo.findOne({ where: { id: userId } });
        if (!user) {
            throw new common_1.UnauthorizedException('User not found');
        }
        const isValid = await bcrypt.compare(dto.currentPassword, user.password_hash);
        if (!isValid) {
            throw new common_1.UnauthorizedException('Current password is incorrect');
        }
        if (dto.newPassword !== dto.confirmPassword) {
            throw new common_1.BadRequestException('New passwords do not match');
        }
        const passwordHash = await bcrypt.hash(dto.newPassword, 12);
        user.password_hash = passwordHash;
        await this.userRepo.save(user);
        await this.logSecurityEvent(userId, 'PASSWORD_CHANGE', {
            timestamp: new Date(),
        });
    }
    async updateSecuritySettings(userId, settingsDto) {
        let settings = await this.securitySettingsRepo.findOne({
            where: { userId },
        });
        if (!settings) {
            const newSettings = this.securitySettingsRepo.create({
                userId,
                ...settingsDto,
            });
            settings = await this.securitySettingsRepo.save(newSettings);
        }
        else {
            Object.assign(settings, settingsDto);
            settings = await this.securitySettingsRepo.save(settings);
        }
        await this.logSecurityEvent(userId, 'SECURITY_SETTINGS_UPDATE', {
            changes: settingsDto,
            timestamp: new Date(),
        });
    }
    async getActiveSessions(userId) {
        return this.sessionRepo.find({
            where: {
                userId,
                isActive: true,
            },
            order: {
                lastAccessed: 'DESC',
            },
        });
    }
    async revokeSession(userId, sessionId) {
        const session = await this.sessionRepo.findOne({
            where: {
                id: sessionId,
                userId,
            },
        });
        if (!session) {
            throw new common_1.BadRequestException('Session not found');
        }
        session.isActive = false;
        await this.sessionRepo.save(session);
        if (session.token) {
            await this.tokenBlacklistService.addToBlacklist(session.token, 7 * 24 * 60 * 60);
        }
        await this.logSecurityEvent(userId, 'SESSION_REVOKED', {
            sessionId,
            deviceId: session.deviceId,
            timestamp: new Date(),
        });
    }
    async logSecurityEvent(userId, eventType, eventData) {
        const event = this.securityEventRepo.create({
            userId,
            eventType,
            eventData,
            ipAddress: eventData.ipAddress || 'unknown',
            deviceId: eventData.deviceId,
            userAgent: eventData.userAgent,
        });
        await this.securityEventRepo.save(event);
    }
    async getSecurityEvents(userId, limit = 50) {
        return this.securityEventRepo.find({
            where: { userId },
            order: { timestamp: 'DESC' },
            take: limit,
        });
    }
    async generateVerificationToken() {
        return crypto.randomBytes(32).toString('hex');
    }
    async sendVerificationEmail(user) {
        const verificationToken = await this.generateVerificationToken();
        const verificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000);
        let settings = await this.securitySettingsRepo.findOne({
            where: { userId: user.id },
        });
        const verificationData = {
            userId: user.id,
            emailVerificationToken: verificationToken,
            emailVerificationExpires: verificationExpires,
            isEmailVerified: false,
        };
        if (!settings) {
            const newSettings = this.securitySettingsRepo.create(verificationData);
            settings = await this.securitySettingsRepo.save(newSettings);
        }
        else {
            Object.assign(settings, verificationData);
            settings = await this.securitySettingsRepo.save(settings);
        }
        if (process.env.NODE_ENV === 'development') {
            console.log(`Verification link for ${user.email}: http://localhost:3000/auth/verify-email?token=${verificationToken}`);
        }
    }
    async verifyEmail(token) {
        const settings = await this.securitySettingsRepo.findOne({
            where: { emailVerificationToken: token },
        });
        if (!settings) {
            throw new common_1.UnauthorizedException('Invalid verification token');
        }
        if (!settings.emailVerificationExpires || settings.emailVerificationExpires < new Date()) {
            throw new common_1.UnauthorizedException('Verification token has expired');
        }
        settings.isEmailVerified = true;
        settings.emailVerificationToken = null;
        settings.emailVerificationExpires = null;
        await this.securitySettingsRepo.save(settings);
        return true;
    }
    async resendVerificationEmail(userId) {
        const user = await this.userRepo.findOne({ where: { id: userId } });
        if (!user) {
            throw new common_1.UnauthorizedException('User not found');
        }
        const settings = await this.securitySettingsRepo.findOne({
            where: { userId },
        });
        if (settings?.isEmailVerified) {
            throw new common_1.BadRequestException('Email is already verified');
        }
        await this.sendVerificationEmail(user);
    }
    async isEmailVerified(userId) {
        const settings = await this.securitySettingsRepo.findOne({
            where: { userId },
        });
        return settings?.isEmailVerified ?? false;
    }
    async getSecuritySettings(userId) {
        const settings = await this.securitySettingsRepo.findOne({
            where: { userId },
        });
        if (!settings) {
            throw new common_1.NotFoundException('Security settings not found');
        }
        return settings;
    }
    async generateNewBackupCodes(userId) {
        const settings = await this.securitySettingsRepo.findOne({
            where: { userId },
        });
        if (!settings) {
            throw new common_1.NotFoundException('Security settings not found');
        }
        const newCodes = this.generateBackupCodes();
        settings.backupCodes = newCodes;
        await this.securitySettingsRepo.save(settings);
        return newCodes;
    }
    async initiateAccountRecovery(email) {
        console.log(`Initiating account recovery for ${email}`);
    }
    async verifyRecoveryRequest(token, answers) {
        console.log(`Verifying recovery request with token ${token} and answers ${JSON.stringify(answers)}`);
        return true;
    }
};
exports.SecurityService = SecurityService;
exports.SecurityService = SecurityService = SecurityService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(security_entity_1.UserSecuritySettings)),
    __param(1, (0, typeorm_1.InjectRepository)(security_entity_1.UserSession)),
    __param(2, (0, typeorm_1.InjectRepository)(security_entity_1.SecurityEvent)),
    __param(3, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _c : Object, typeof (_d = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _d : Object, config_1.ConfigService,
        token_blacklist_service_1.TokenBlacklistService])
], SecurityService);
//# sourceMappingURL=security.service.js.map