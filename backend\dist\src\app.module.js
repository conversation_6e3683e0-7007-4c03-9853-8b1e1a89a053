"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const cache_module_1 = require("./cache/cache.module");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const users_module_1 = require("./modules/users/users.module");
const auth_module_1 = require("./modules/auth/auth.module");
const materials_module_1 = require("./modules/materials/materials.module");
const units_module_1 = require("./modules/units/units.module");
const progress_module_1 = require("./progress/progress.module");
const quiz_module_1 = require("./quiz/quiz.module");
const notifications_module_1 = require("./notifications/notifications.module");
const feedback_module_1 = require("./feedback/feedback.module");
const analytics_module_1 = require("./analytics/analytics.module");
const health_module_1 = require("./health/health.module");
const throttler_module_1 = require("./modules/throttler/throttler.module");
const test_module_1 = require("./test/test.module");
const roles_module_1 = require("./modules/roles/roles.module");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
            }),
            typeorm_1.TypeOrmModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: (configService) => ({
                    type: 'postgres',
                    host: configService.get('POSTGRES_HOST'),
                    port: parseInt(configService.get('POSTGRES_PORT', '5432')),
                    username: configService.get('POSTGRES_USER'),
                    password: String(configService.get('POSTGRES_PASSWORD')),
                    database: configService.get('POSTGRES_DB'),
                    entities: [__dirname + '/**/*.entity{.ts,.js}'],
                    synchronize: false,
                    migrations: [__dirname + '/migrations/*{.ts,.js}'],
                    migrationsRun: false,
                    migrationsTableName: 'migrations',
                    ssl: false,
                }),
                inject: [config_1.ConfigService],
            }),
            cache_module_1.CacheModule,
            users_module_1.UsersModule,
            auth_module_1.AuthModule,
            materials_module_1.MaterialsModule,
            units_module_1.UnitsModule,
            progress_module_1.ProgressModule,
            quiz_module_1.QuizModule,
            notifications_module_1.NotificationsModule,
            feedback_module_1.FeedbackModule,
            analytics_module_1.AnalyticsModule,
            health_module_1.HealthModule,
            throttler_module_1.AppThrottlerModule,
            test_module_1.TestModule,
            roles_module_1.RolesModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map