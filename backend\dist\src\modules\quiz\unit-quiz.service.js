"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnitQuizService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const unit_quiz_entity_1 = require("../../entities/unit-quiz.entity");
const unit_entity_1 = require("../../entities/unit.entity");
const quiz_question_entity_1 = require("../../entities/quiz-question.entity");
let UnitQuizService = class UnitQuizService {
    constructor(unitQuizRepo, unitRepo, questionRepo) {
        this.unitQuizRepo = unitQuizRepo;
        this.unitRepo = unitRepo;
        this.questionRepo = questionRepo;
    }
    async generateQuiz(unitId) {
        throw new Error('Not implemented');
    }
    async validateAttempt(userId, unitId) {
        throw new Error('Not implemented');
    }
    async getQuizForUser(unitId) {
        const quiz = await this.unitQuizRepo.findOne({ where: { unit: { id: unitId }, isPublished: true }, relations: ['questions'] });
        if (!quiz)
            throw new common_1.NotFoundException('Quiz not found');
        return {
            title: quiz.title,
            instructions: quiz.instructions,
            questions: quiz.questions.map(q => ({
                id: q.id,
                text: q.text,
                options: q.options,
            })),
        };
    }
    async scoreQuiz(userId, unitId, answers) {
        const quiz = await this.unitQuizRepo.findOne({ where: { unit: { id: unitId }, isPublished: true }, relations: ['questions'] });
        if (!quiz)
            throw new common_1.NotFoundException('Quiz not found');
        let correct = 0;
        quiz.questions.forEach(q => {
            if (answers[q.id] === q.correct_answer)
                correct++;
        });
        const score = Math.round((correct / quiz.questions.length) * 100);
        const passed = score >= 70;
        return { score, passed, feedback: passed ? "Great job!" : "Review the topics and try again." };
    }
};
exports.UnitQuizService = UnitQuizService;
exports.UnitQuizService = UnitQuizService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(unit_quiz_entity_1.UnitQuiz)),
    __param(1, (0, typeorm_1.InjectRepository)(unit_entity_1.Unit)),
    __param(2, (0, typeorm_1.InjectRepository)(quiz_question_entity_1.QuizQuestion)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], UnitQuizService);
//# sourceMappingURL=unit-quiz.service.js.map