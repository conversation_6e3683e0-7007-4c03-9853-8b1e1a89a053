﻿<?xml version="1.0" encoding="utf-8"?>
<Project>
  <ProjectOutputs>
    <ProjectOutput>
      <FullPath>C:\Users\<USER>\medical\backend\node_modules\.pnpm\@tensorflow+tfjs-node@4.22._cff3ae2e40e80be74cb56d80af0341da\node_modules\@tensorflow\tfjs-node\build\Release\tfjs_binding.node</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>C:\Users\<USER>\medical\backend\node_modules\.pnpm\@tensorflow+tfjs-node@4.22._cff3ae2e40e80be74cb56d80af0341da\node_modules\@tensorflow\tfjs-node\build\Release\action_after_build</FullPath>
    </ProjectOutput>
  </ProjectOutputs>
  <ContentFiles />
  <SatelliteDlls />
  <NonRecipeFileRefs />
</Project>