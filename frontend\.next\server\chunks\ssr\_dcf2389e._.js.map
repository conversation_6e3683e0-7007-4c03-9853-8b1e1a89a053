{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/app/ClientLayout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ClientLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call ClientLayout() from the server but ClientLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/ClientLayout.tsx <module evaluation>\",\n    \"ClientLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0DACA", "debugId": null}}, {"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/app/ClientLayout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ClientLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call ClientLayout() from the server but ClientLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/ClientLayout.tsx\",\n    \"ClientLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,sCACA", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/app/providers.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Providers = registerClientReference(\n    function() { throw new Error(\"Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/providers.tsx <module evaluation>\",\n    \"Providers\",\n);\nexport const useTheme = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/providers.tsx <module evaluation>\",\n    \"useTheme\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,uDACA;AAEG,MAAM,WAAW,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,uDACA", "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/app/providers.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Providers = registerClientReference(\n    function() { throw new Error(\"Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/providers.tsx\",\n    \"Providers\",\n);\nexport const useTheme = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/providers.tsx\",\n    \"useTheme\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,mCACA;AAEG,MAAM,WAAW,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,mCACA", "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/app/layout.tsx"], "sourcesContent": ["// app/layout.tsx\nimport './globals.css';\nimport { ReactNode } from 'react';\nimport { ClientLayout } from './ClientLayout';\nimport { Providers } from './providers';\n\nexport const metadata = {\n  metadataBase: new URL('http://localhost:3000'),\n  title: 'MedTrack Hub | Medical Learning Platform',\n  description: 'Track your medical study progress, assess your readiness, and stay ahead.',\n  keywords: ['MedTrack Hub', 'MedTrack', 'medical education', 'quiz app', 'study tracking', 'medical students'],\n  openGraph: {\n    title: 'MedTrack Hub | Medical Learning Platform',\n    description: 'Track your medical study progress, assess your readiness, and stay ahead.',\n    url: 'https://medtrackhub.com', // replace with actual domain\n    siteName: 'MedTrack Hub',\n    locale: 'en_US',\n    type: 'website',\n    images: [\n      {\n        url: '/og-image.png', // replace with your actual OG image\n        width: 1200,\n        height: 630,\n        alt: 'MedTrack Hub Banner',\n      },\n    ],\n  },\n  twitter: {\n    card: 'summary_large_image',\n    title: 'MedTrack Hub | Medical Learning Platform',\n    description: 'Track your medical study progress, assess your readiness, and stay ahead.',\n    images: ['/og-image.png'],\n  },\n  icons: {\n    icon: '/favicon.ico',\n    shortcut: '/favicon-32x32.png',\n    apple: '/apple-touch-icon.png',\n  },\n  manifest: '/site.webmanifest',\n};\n\nexport const viewport = {\n  width: 'device-width',\n  initialScale: 1,\n  themeColor: '#1e40af',\n};\n\ninterface RootLayoutProps {\n  children: ReactNode;\n}\n\nexport default function RootLayout({ children }: RootLayoutProps) {\n  return (\n    <html lang=\"en\">\n      <body>\n        <Providers>\n          <ClientLayout>\n            {children}\n          </ClientLayout>\n        </Providers>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": "AAAA,iBAAiB;;;;;;;AAGjB;AACA;;;;;AAEO,MAAM,WAAW;IACtB,cAAc,IAAI,IAAI;IACtB,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAgB;QAAY;QAAqB;QAAY;QAAkB;KAAmB;IAC7G,WAAW;QACT,OAAO;QACP,aAAa;QACb,KAAK;QACL,UAAU;QACV,QAAQ;QACR,MAAM;QACN,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;IACH;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;SAAgB;IAC3B;IACA,OAAO;QACL,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,UAAU;AACZ;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,cAAc;IACd,YAAY;AACd;AAMe,SAAS,WAAW,EAAE,QAAQ,EAAmB;IAC9D,qBACE,6WAAC;QAAK,MAAK;kBACT,cAAA,6WAAC;sBACC,cAAA,6WAAC,wHAAA,CAAA,YAAS;0BACR,cAAA,6WAAC,2HAAA,CAAA,eAAY;8BACV;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/node_modules/.pnpm/next%4015.4.2_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,uMACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}