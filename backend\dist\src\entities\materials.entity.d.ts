import { User } from './user.entity';
import { Unit } from './unit.entity';
import { MaterialShare } from './material_shares.entity';
import { Progress } from './progress.entity';
export declare enum MaterialType {
    EXAM_PREP = "exam_prep",
    CLINICAL_UPDATE = "clinical_update",
    GUIDELINE = "guideline",
    CASE_STUDY = "case_study",
    QUIZ = "quiz",
    ARTICLE = "article",
    VIDEO = "video",
    PRESENTATION = "presentation",
    WORKSHOP = "workshop",
    CONFERENCE = "conference",
    RESEARCH = "research"
}
export declare enum ContentSource {
    KENYA_GUIDELINES = "kenya_guidelines",
    KEMRI = "kemri",
    WHO = "who",
    LOCAL = "local",
    INTERNATIONAL = "international"
}
export declare class Material {
    id: string;
    title: string;
    description: string;
    type: MaterialType;
    content: string;
    file_url: string;
    answer: string;
    is_exam_focused: boolean;
    is_clinical_update: boolean;
    source: ContentSource;
    is_cpd_eligible: boolean;
    metadata: {
        cpd_points?: number;
        difficulty?: string;
        target_audience?: string[];
        tags?: string[];
        references?: string[];
    };
    category: string;
    difficulty: number;
    author: User;
    unit: Unit;
    user: User;
    shares: MaterialShare[];
    progress: Progress[];
    createdAt: Date;
    updatedAt: Date;
}
