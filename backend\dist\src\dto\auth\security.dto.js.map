{"version": 3, "file": "security.dto.js", "sourceRoot": "", "sources": ["../../../../src/dto/auth/security.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AACA,qDAAsF;AACtF,6CAA8C;AAE9C,MAAa,mBAAmB;CAmB/B;AAnBD,kDAmBC;AAfC;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,kCAAkC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACjF,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;;6DACe;AAK3B;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,6BAA6B,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC5E,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;;+DACiB;AAK7B;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,gCAAgC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC/E,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;;kEACoB;AAIhC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,4BAA4B,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC3E,IAAA,4BAAU,GAAE;;2DACW;AAG1B,MAAa,iBAAiB;CAe7B;AAfD,8CAeC;AAXC;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IAChD,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,CAAC,CAAC;;0DACW;AAKxB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,cAAc,EAAE,CAAC;IAC5C,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,CAAC,CAAC;;sDACO;AAKpB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,sBAAsB,EAAE,CAAC;IACpD,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,CAAC,CAAC;;0DACW;AAG1B,MAAa,iBAAiB;CAI7B;AAJD,8CAIC;AADC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,uBAAuB,EAAE,CAAC;IACrD,IAAA,0BAAQ,GAAE;;iDACI;AAGjB,MAAa,kBAAkB;CAK9B;AALD,gDAKC;AADC;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,mCAAmC,EAAE,CAAC;IACjE,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,CAAC,CAAC;;iDACC;AAGhB,MAAa,oBAAoB;CAIhC;AAJD,oDAIC;AADC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,0BAA0B,EAAE,CAAC;IACxD,IAAA,0BAAQ,GAAE;;mDACG;AAGhB,MAAa,qBAAqB;CAIjC;AAJD,sDAIC;AADC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,sCAAsC,EAAE,CAAC;IACpE,IAAA,yBAAO,GAAE;;oDACI;AAGhB,MAAa,gBAAgB;CAI5B;AAJD,4CAIC;AADC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,sBAAsB,EAAE,CAAC;IACpD,IAAA,0BAAQ,GAAE;;mDACO;AAGpB,MAAa,kBAAkB;CAI9B;AAJD,gDAIC;AADC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,oCAAoC,EAAE,CAAC;IAClE,IAAA,yBAAO,GAAE;;iDACI;AAGhB,MAAa,iBAAiB;CAO7B;AAPD,8CAOC;AAJC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,CAAC;IAC9C,IAAA,0BAAQ,GAAE;;gDACG;AAGd;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,2BAA2B,EAAE,CAAC;;kDACvB"}