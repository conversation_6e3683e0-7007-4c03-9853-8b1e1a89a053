"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FlashcardsController = void 0;
const common_1 = require("@nestjs/common");
const flashcards_service_1 = require("./flashcards.service");
const jwt_auth_guard_1 = require("../modules/auth/jwt-auth.guard");
let FlashcardsController = class FlashcardsController {
    constructor(flashcardsService) {
        this.flashcardsService = flashcardsService;
    }
    async createFlashcard(userId, questionId) {
        return this.flashcardsService.createFlashcard(userId, questionId);
    }
    async getDueCards(userId) {
        return this.flashcardsService.getDueCards(userId);
    }
    async updateCard(cardId, quality) {
        return this.flashcardsService.updateCard(cardId, quality);
    }
    async getCardStats(userId) {
        return this.flashcardsService.getCardStats(userId);
    }
    async syncCards(userId, cards) {
        return this.flashcardsService.syncCards(userId, cards);
    }
};
exports.FlashcardsController = FlashcardsController;
__decorate([
    (0, common_1.Post)('create'),
    __param(0, (0, common_1.Body)('userId')),
    __param(1, (0, common_1.Body)('questionId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], FlashcardsController.prototype, "createFlashcard", null);
__decorate([
    (0, common_1.Get)('due/:userId'),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FlashcardsController.prototype, "getDueCards", null);
__decorate([
    (0, common_1.Post)('update/:cardId'),
    __param(0, (0, common_1.Param)('cardId')),
    __param(1, (0, common_1.Body)('quality')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number]),
    __metadata("design:returntype", Promise)
], FlashcardsController.prototype, "updateCard", null);
__decorate([
    (0, common_1.Get)('stats/:userId'),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FlashcardsController.prototype, "getCardStats", null);
__decorate([
    (0, common_1.Post)('sync/:userId'),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Body)('cards')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Array]),
    __metadata("design:returntype", Promise)
], FlashcardsController.prototype, "syncCards", null);
exports.FlashcardsController = FlashcardsController = __decorate([
    (0, common_1.Controller)('flashcards'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [flashcards_service_1.FlashcardsService])
], FlashcardsController);
//# sourceMappingURL=flashcards.controller.js.map