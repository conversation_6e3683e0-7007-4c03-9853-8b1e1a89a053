"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuizService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const quiz_question_entity_1 = require("../entities/quiz-question.entity");
const user_response_entity_1 = require("../entities/user-response.entity");
let QuizService = class QuizService {
    constructor(quizQuestionRepository, userResponseRepository) {
        this.quizQuestionRepository = quizQuestionRepository;
        this.userResponseRepository = userResponseRepository;
    }
    async getQuestionsByUnit(unitId) {
        return this.quizQuestionRepository.find({
            where: { unit: { id: unitId } }
        });
    }
    async submitAnswer(userId, questionId, answer) {
        const question = await this.quizQuestionRepository.findOne({
            where: { id: questionId }
        });
        if (!question) {
            throw new common_1.NotFoundException('Question not found');
        }
        const isCorrect = question.correct_answer === answer;
        const response = this.userResponseRepository.create({
            user: { id: userId },
            question: { id: questionId },
            selected_answer: answer,
            is_correct: isCorrect
        });
        return this.userResponseRepository.save(response);
    }
    async getUserQuizResults(userId, unitId) {
        const responses = await this.userResponseRepository.find({
            where: {
                user: { id: userId },
                question: { unit: { id: unitId } }
            }
        });
        const total = responses.length;
        const correct = responses.filter((r) => r.is_correct).length;
        return {
            total,
            correct,
            percentage: total > 0 ? (correct / total) * 100 : 0
        };
    }
};
exports.QuizService = QuizService;
exports.QuizService = QuizService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(quiz_question_entity_1.QuizQuestion)),
    __param(1, (0, typeorm_1.InjectRepository)(user_response_entity_1.UserResponse)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object])
], QuizService);
//# sourceMappingURL=quiz.service.js.map