"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.linSpaceConfig = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var nodejs_kernel_backend_1 = require("../nodejs_kernel_backend");
exports.linSpaceConfig = {
    kernelName: tfjs_1.LinSpace,
    backendName: 'tensorflow',
    kernelFunc: function (args) {
        var backend = args.backend;
        var _a = args.attrs, start = _a.start, stop = _a.stop, num = _a.num;
        var opAttrs = [
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('T', 'float32'),
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('Tidx', 'int32')
        ];
        return (0, tfjs_1.tidy)(function () {
            var inputs = [
                (0, tfjs_1.scalar)(start, 'float32'), (0, tfjs_1.scalar)(stop, 'float32'), (0, tfjs_1.scalar)(num, 'int32')
            ];
            return backend.executeSingleOutput(tfjs_1.LinSpace, opAttrs, inputs);
        });
    }
};
