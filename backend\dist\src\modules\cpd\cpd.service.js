"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CPDService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const cpd_tracking_entity_1 = require("../../entities/cpd-tracking.entity");
const user_entity_1 = require("../../entities/user.entity");
const materials_entity_1 = require("../../entities/materials.entity");
let CPDService = class CPDService {
    constructor(cpdActivityRepository, cpdCycleRepository, userRepository, materialRepository) {
        this.cpdActivityRepository = cpdActivityRepository;
        this.cpdCycleRepository = cpdCycleRepository;
        this.userRepository = userRepository;
        this.materialRepository = materialRepository;
    }
    async createCPDActivity(userId, data) {
        const user = await this.userRepository.findOne({ where: { id: userId } });
        if (!user)
            throw new Error('User not found');
        const activity = this.cpdActivityRepository.create({
            title: data.title,
            description: data.description,
            points: data.points,
            activity_date: new Date(),
            is_verified: false,
            user: user
        });
        if (data.materialId) {
            const material = await this.materialRepository.findOne({ where: { id: data.materialId } });
            if (material) {
            }
        }
        return await this.cpdActivityRepository.save(activity);
    }
    async getCurrentCPDCycle(userId) {
        const user = await this.userRepository.findOne({ where: { id: userId } });
        if (!user)
            throw new Error('User not found');
        const currentDate = new Date();
        let cycle = await this.cpdCycleRepository.findOne({
            where: {
                user: { id: userId },
                start_date: (0, typeorm_2.LessThanOrEqual)(currentDate),
                end_date: (0, typeorm_2.MoreThanOrEqual)(currentDate),
            },
        });
        if (!cycle) {
            cycle = this.cpdCycleRepository.create({
                user: { id: userId },
                start_date: new Date(),
                end_date: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
                total_points: 0,
                required_points: 0,
                is_completed: false
            });
            cycle = await this.cpdCycleRepository.save(cycle);
        }
        return cycle;
    }
    async getCPDActivities(userId, options = {}) {
        const query = this.cpdActivityRepository.createQueryBuilder('activity')
            .where('activity.user.id = :userId', { userId });
        if (options.startDate) {
            query.andWhere('activity.activity_date >= :startDate', { startDate: options.startDate });
        }
        if (options.endDate) {
            query.andWhere('activity.activity_date <= :endDate', { endDate: options.endDate });
        }
        if (options.activityType) {
            query.andWhere('activity.activity_type = :activityType', { activityType: options.activityType });
        }
        if (options.isVerified !== undefined) {
            query.andWhere('activity.is_verified = :isVerified', { isVerified: options.isVerified });
        }
        return query.getMany();
    }
    async verifyCPDActivity(activityId, verified, notes) {
        const activity = await this.cpdActivityRepository.findOne({ where: { id: activityId } });
        if (!activity)
            throw new Error('Activity not found');
        activity.is_verified = verified;
        if (notes)
            activity.verification_notes = notes;
        return this.cpdActivityRepository.save(activity);
    }
    async updateCPDCycle(cycleId, data) {
        const cycle = await this.cpdCycleRepository.findOne({ where: { id: cycleId } });
        if (!cycle)
            throw new Error('Cycle not found');
        if (data.required_points !== undefined) {
            cycle.required_points = data.required_points;
        }
        return this.cpdCycleRepository.save(cycle);
    }
};
exports.CPDService = CPDService;
exports.CPDService = CPDService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(cpd_tracking_entity_1.CPDActivity)),
    __param(1, (0, typeorm_1.InjectRepository)(cpd_tracking_entity_1.CPDCycle)),
    __param(2, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(3, (0, typeorm_1.InjectRepository)(materials_entity_1.Material)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], CPDService);
//# sourceMappingURL=cpd.service.js.map