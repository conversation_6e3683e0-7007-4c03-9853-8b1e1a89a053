{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/components/auth/AuthForm.tsx"], "sourcesContent": ["import { ReactNode, FormEvent } from 'react';\r\nimport { Loader2 } from 'lucide-react';\r\n\r\ninterface AuthFormProps {\r\n  title: string;\r\n  subtitle?: string;\r\n  children: ReactNode;\r\n  onSubmit: (e: FormEvent<HTMLFormElement>) => void;\r\n  isLoading?: boolean;\r\n  submitText: string;\r\n  footer?: ReactNode;\r\n}\r\n\r\nexport function AuthForm({ \r\n  title, \r\n  subtitle, \r\n  children, \r\n  onSubmit, \r\n  isLoading = false, \r\n  submitText,\r\n  footer \r\n}: AuthFormProps) {\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"text-center\">\r\n        <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\r\n          {title}\r\n        </h1>\r\n        {subtitle && (\r\n          <p className=\"mt-2 text-sm text-gray-600 dark:text-gray-400\">\r\n            {subtitle}\r\n          </p>\r\n        )}\r\n      </div>\r\n\r\n      <form onSubmit={onSubmit} className=\"space-y-4\">\r\n        {children}\r\n        \r\n        <button\r\n          type=\"submit\"\r\n          disabled={isLoading}\r\n          className=\"w-full flex justify-center items-center px-4 py-3 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors dark:focus:ring-offset-gray-800\"\r\n        >\r\n          {isLoading ? (\r\n            <>\r\n              <Loader2 className=\"animate-spin -ml-1 mr-2 h-4 w-4\" />\r\n              Processing...\r\n            </>\r\n          ) : (\r\n            submitText\r\n          )}\r\n        </button>\r\n      </form>\r\n\r\n      {footer && (\r\n        <div className=\"mt-6\">\r\n          {footer}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AACA;;;AAYO,SAAS,SAAS,EACvB,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,YAAY,KAAK,EACjB,UAAU,EACV,MAAM,EACQ;IACd,qBACE,6WAAC;QAAI,WAAU;;0BACb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAG,WAAU;kCACX;;;;;;oBAEF,0BACC,6WAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;0BAKP,6WAAC;gBAAK,UAAU;gBAAU,WAAU;;oBACjC;kCAED,6WAAC;wBACC,MAAK;wBACL,UAAU;wBACV,WAAU;kCAET,0BACC;;8CACE,6WAAC,gSAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAAoC;;2CAIzD;;;;;;;;;;;;YAKL,wBACC,6WAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/components/auth/FormField.tsx"], "sourcesContent": ["import { InputHTMLAttributes, forwardRef } from 'react';\r\nimport { AlertCircle } from 'lucide-react';\r\n\r\ninterface FormFieldProps extends InputHTMLAttributes<HTMLInputElement> {\r\n  label: string;\r\n  error?: string;\r\n  helperText?: string;\r\n}\r\n\r\nexport const FormField = forwardRef<HTMLInputElement, FormFieldProps>(\r\n  ({ label, error, helperText, className = '', ...props }, ref) => {\r\n    return (\r\n      <div className=\"space-y-1\">\r\n        <label \r\n          htmlFor={props.id || props.name}\r\n          className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\"\r\n        >\r\n          {label}\r\n        </label>\r\n        \r\n        <input\r\n          ref={ref}\r\n          className={`\r\n            w-full px-3 py-2 border rounded-lg shadow-sm placeholder-gray-400 \r\n            focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\r\n            dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:placeholder-gray-500\r\n            ${error \r\n              ? 'border-red-300 focus:ring-red-500 focus:border-red-500' \r\n              : 'border-gray-300 dark:border-gray-600'\r\n            }\r\n            ${className}\r\n          `}\r\n          {...props}\r\n        />\r\n        \r\n        {error && (\r\n          <div className=\"flex items-center space-x-1 text-sm text-red-600 dark:text-red-400\">\r\n            <AlertCircle className=\"h-4 w-4\" />\r\n            <span>{error}</span>\r\n          </div>\r\n        )}\r\n        \r\n        {helperText && !error && (\r\n          <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n            {helperText}\r\n          </p>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n);\r\n\r\nFormField.displayName = 'FormField'; "], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQO,MAAM,0BAAY,CAAA,GAAA,oUAAA,CAAA,aAAU,AAAD,EAChC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,YAAY,EAAE,EAAE,GAAG,OAAO,EAAE;IACvD,qBACE,6WAAC;QAAI,WAAU;;0BACb,6WAAC;gBACC,SAAS,MAAM,EAAE,IAAI,MAAM,IAAI;gBAC/B,WAAU;0BAET;;;;;;0BAGH,6WAAC;gBACC,KAAK;gBACL,WAAW,CAAC;;;;YAIV,EAAE,QACE,2DACA,uCACH;YACD,EAAE,UAAU;UACd,CAAC;gBACA,GAAG,KAAK;;;;;;YAGV,uBACC,6WAAC;gBAAI,WAAU;;kCACb,6WAAC,wSAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6WAAC;kCAAM;;;;;;;;;;;;YAIV,cAAc,CAAC,uBACd,6WAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAGF,UAAU,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/components/auth/Notification.tsx"], "sourcesContent": ["import { useEffect } from 'react';\r\nimport { CheckCircle, XCircle, AlertCircle, X } from 'lucide-react';\r\n\r\ninterface NotificationProps {\r\n  type: 'success' | 'error' | 'warning';\r\n  message: string;\r\n  onClose: () => void;\r\n  autoClose?: boolean;\r\n  duration?: number;\r\n}\r\n\r\nexport function Notification({ \r\n  type, \r\n  message, \r\n  onClose, \r\n  autoClose = true, \r\n  duration = 5000 \r\n}: NotificationProps) {\r\n  useEffect(() => {\r\n    if (autoClose) {\r\n      const timer = setTimeout(onClose, duration);\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [autoClose, duration, onClose]);\r\n\r\n  const icons = {\r\n    success: <CheckCircle className=\"h-5 w-5 text-green-500\" />,\r\n    error: <XCircle className=\"h-5 w-5 text-red-500\" />,\r\n    warning: <AlertCircle className=\"h-5 w-5 text-yellow-500\" />\r\n  };\r\n\r\n  const bgColors = {\r\n    success: 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800',\r\n    error: 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800',\r\n    warning: 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'\r\n  };\r\n\r\n  const textColors = {\r\n    success: 'text-green-800 dark:text-green-200',\r\n    error: 'text-red-800 dark:text-red-200',\r\n    warning: 'text-yellow-800 dark:text-yellow-200'\r\n  };\r\n\r\n  return (\r\n    <div className={`p-4 rounded-lg border ${bgColors[type]} ${textColors[type]}`}>\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          {icons[type]}\r\n          <span className=\"text-sm font-medium\">{message}</span>\r\n        </div>\r\n        <button\r\n          onClick={onClose}\r\n          className=\"ml-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\r\n        >\r\n          <X className=\"h-4 w-4\" />\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;;;;AAUO,SAAS,aAAa,EAC3B,IAAI,EACJ,OAAO,EACP,OAAO,EACP,YAAY,IAAI,EAChB,WAAW,IAAI,EACG;IAClB,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,MAAM,QAAQ,WAAW,SAAS;YAClC,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAW;QAAU;KAAQ;IAEjC,MAAM,QAAQ;QACZ,uBAAS,6WAAC,wSAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QAChC,qBAAO,6WAAC,gSAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QAC1B,uBAAS,6WAAC,wSAAA,CAAA,cAAW;YAAC,WAAU;;;;;;IAClC;IAEA,MAAM,WAAW;QACf,SAAS;QACT,OAAO;QACP,SAAS;IACX;IAEA,MAAM,aAAa;QACjB,SAAS;QACT,OAAO;QACP,SAAS;IACX;IAEA,qBACE,6WAAC;QAAI,WAAW,CAAC,sBAAsB,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,KAAK,EAAE;kBAC3E,cAAA,6WAAC;YAAI,WAAU;;8BACb,6WAAC;oBAAI,WAAU;;wBACZ,KAAK,CAAC,KAAK;sCACZ,6WAAC;4BAAK,WAAU;sCAAuB;;;;;;;;;;;;8BAEzC,6WAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6WAAC,gRAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKvB", "debugId": null}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/app/auth/register/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport Head from 'next/head';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON>aEnvelope, <PERSON><PERSON><PERSON>ock, <PERSON>a<PERSON><PERSON>ner, FaUserTag, FaCheck, FaTimes, FaInfoCircle, FaEye, FaEyeSlash } from 'react-icons/fa';\nimport { useAuth } from '@/contexts/AuthContext';\nimport dynamic from 'next/dynamic';\nimport { Suspense } from 'react';\nimport { AuthForm } from '@/components/auth/AuthForm';\nimport { FormField } from '@/components/auth/FormField';\nimport { Notification } from '@/components/auth/Notification';\n\ninterface FormData {\n  email: string;\n  username: string;\n  name: string;\n  password: string;\n  confirmPassword: string;\n  role: string;\n  acceptTerms: boolean;\n}\n\ninterface FormErrors {\n  email?: string;\n  username?: string;\n  name?: string;\n  password?: string;\n  confirmPassword?: string;\n  acceptTerms?: string;\n}\n\n// Helper function to generate a unique username\nconst generateUniqueUsername = (baseUsername: string): string => {\n  // Add a random number between 100-999 to make it more likely to be unique\n  return `${baseUsername}${Math.floor(Math.random() * 900) + 100}`;\n};\n\n// Helper function to generate a name from email\nconst generateNameFromEmail = (email: string): string => {\n  // Get the part before @ and replace dots/underscores with spaces\n  const namePart = email.split('@')[0].replace(/[._]/g, ' ');\n\n  // Capitalize each word\n  return namePart\n    .split(' ')\n    .map(word => word.charAt(0).toUpperCase() + word.slice(1))\n    .join(' ');\n};\n\n// Error fallback component\nfunction ErrorFallback({ error, resetErrorBoundary }: { error: Error; resetErrorBoundary: () => void }) {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div className=\"rounded-md bg-red-50 p-4\">\n          <div className=\"flex\">\n            <div className=\"flex-shrink-0\">\n              <FaTimes className=\"h-5 w-5 text-red-400\" />\n            </div>\n            <div className=\"ml-3\">\n              <h3 className=\"text-sm font-medium text-red-800\">Something went wrong</h3>\n              <div className=\"mt-2 text-sm text-red-700\">\n                <p>{error.message}</p>\n              </div>\n              <div className=\"mt-4\">\n                <button\n                  type=\"button\"\n                  onClick={resetErrorBoundary}\n                  className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\"\n                >\n                  Try again\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n// API configuration\nconst API_CONFIG = {\n  baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3002',\n  timeout: 10000, // 10 seconds\n  retryAttempts: 3,\n};\n\n// API client with retry logic\nasync function apiClient(endpoint: string, options: RequestInit = {}) {\n  let lastError: Error | null = null;\n  \n  for (let attempt = 0; attempt < API_CONFIG.retryAttempts; attempt++) {\n    try {\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.timeout);\n\n      const response = await fetch(`${API_CONFIG.baseUrl}${endpoint}`, {\n        ...options,\n        signal: controller.signal,\n      });\n\n      clearTimeout(timeoutId);\n\n      if (!response.ok) {\n        const error = await response.json();\n        throw new Error(error.message || 'API request failed');\n      }\n\n      return await response.json();\n    } catch (error) {\n      lastError = error as Error;\n      if (error instanceof Error && error.name === 'AbortError') {\n        throw new Error('Request timed out');\n      }\n      // Wait before retrying (exponential backoff)\n      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));\n    }\n  }\n\n  throw lastError || new Error('API request failed after multiple attempts');\n}\n\n// Security headers configuration\nconst securityHeaders = {\n  'Content-Security-Policy': \"default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:;\",\n  'X-Content-Type-Options': 'nosniff',\n  'X-Frame-Options': 'DENY',\n  'X-XSS-Protection': '1; mode=block',\n  'Referrer-Policy': 'strict-origin-when-cross-origin',\n  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',\n};\n\n// Analytics configuration\nconst ANALYTICS_CONFIG = {\n  enabled: process.env.NODE_ENV === 'production',\n  endpoint: process.env.NEXT_PUBLIC_ANALYTICS_ENDPOINT,\n  appId: process.env.NEXT_PUBLIC_APP_ID,\n};\n\n// Performance monitoring\nconst performanceMetrics = {\n  pageLoad: 0,\n  formInteraction: 0,\n  apiCalls: 0,\n  errors: 0,\n};\n\n// Track performance metrics\nconst trackPerformance = (metric: keyof typeof performanceMetrics) => {\n  if (ANALYTICS_CONFIG.enabled) {\n    performanceMetrics[metric]++;\n    \n    // Send metrics to analytics endpoint\n    if (ANALYTICS_CONFIG.endpoint) {\n      fetch(ANALYTICS_CONFIG.endpoint, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          appId: ANALYTICS_CONFIG.appId,\n          metric,\n          value: performanceMetrics[metric],\n          timestamp: new Date().toISOString(),\n        }),\n      }).catch(console.error);\n    }\n  }\n};\n\n// Track user interactions\nconst trackUserInteraction = (action: string, details?: Record<string, any>) => {\n  if (ANALYTICS_CONFIG.enabled) {\n    // Send interaction data to analytics endpoint\n    if (ANALYTICS_CONFIG.endpoint) {\n      fetch(ANALYTICS_CONFIG.endpoint, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          appId: ANALYTICS_CONFIG.appId,\n          action,\n          details,\n          timestamp: new Date().toISOString(),\n        }),\n      }).catch(console.error);\n    }\n  }\n};\n\nconst AuthLayout = dynamic(() => import('@/components/auth/AuthLayout').then(mod => mod.default || (() => <div>Layout unavailable</div>)), {\n  ssr: false,\n  loading: () => <div>Loading layout...</div>,\n});\n\nexport default function RegisterPage() {\n  const router = useRouter();\n  const { register } = useAuth();\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    acceptTerms: false\n  });\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError(null);\n\n    if (formData.password !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      return;\n    }\n\n    if (!formData.acceptTerms) {\n      setError('You must accept the terms and conditions');\n      return;\n    }\n\n    setIsLoading(true);\n\n    try {\n      await register(formData);\n      router.push('/dashboard');\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An error occurred during registration');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value, type, checked } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n\n  return (\n    <AuthLayout\n      title=\"Create Account\"\n      description=\"Join our medical education platform\"\n    >\n      {error && (\n        <Notification\n          type=\"error\"\n          message={error}\n          onClose={() => setError(null)}\n        />\n      )}\n\n      <AuthForm\n        title=\"Sign Up\"\n        subtitle=\"Create your account to get started\"\n        onSubmit={handleSubmit}\n        isLoading={isLoading}\n        submitText=\"Create Account\"\n        footer={\n          <p className=\"text-center text-sm text-gray-600 dark:text-gray-400\">\n            Already have an account?{' '}\n            <Link\n              href=\"/auth/login\"\n              className=\"text-blue-600 hover:text-blue-700 font-medium\"\n            >\n              Sign in\n            </Link>\n          </p>\n        }\n      >\n        <div className=\"space-y-4\">\n          <FormField\n            label=\"Full Name\"\n            type=\"text\"\n            name=\"name\"\n            value={formData.name}\n            onChange={handleChange}\n            required\n            placeholder=\"Enter your full name\"\n          />\n\n          <FormField\n            label=\"Email Address\"\n            type=\"email\"\n            name=\"email\"\n            value={formData.email}\n            onChange={handleChange}\n            required\n            placeholder=\"Enter your email\"\n          />\n\n          <FormField\n            label=\"Password\"\n            type=\"password\"\n            name=\"password\"\n            value={formData.password}\n            onChange={handleChange}\n            required\n            placeholder=\"Create a password\"\n            helperText=\"Must be at least 8 characters long\"\n          />\n\n          <FormField\n            label=\"Confirm Password\"\n            type=\"password\"\n            name=\"confirmPassword\"\n            value={formData.confirmPassword}\n            onChange={handleChange}\n            required\n            placeholder=\"Confirm your password\"\n          />\n\n          <div className=\"flex items-start\">\n            <div className=\"flex items-center h-5\">\n              <input\n                type=\"checkbox\"\n                id=\"acceptTerms\"\n                name=\"acceptTerms\"\n                checked={formData.acceptTerms}\n                onChange={handleChange}\n                className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n              />\n            </div>\n            <div className=\"ml-3 text-sm\">\n              <label\n                htmlFor=\"acceptTerms\"\n                className=\"text-gray-600 dark:text-gray-300\"\n              >\n                I agree to the{' '}\n                <Link\n                  href=\"/terms\"\n                  className=\"text-blue-600 hover:text-blue-700 font-medium\"\n                >\n                  Terms of Service\n                </Link>{' '}\n                and{' '}\n                <Link\n                  href=\"/privacy\"\n                  className=\"text-blue-600 hover:text-blue-700 font-medium\"\n                >\n                  Privacy Policy\n                </Link>\n              </label>\n            </div>\n          </div>\n        </div>\n      </AuthForm>\n    </AuthLayout>\n  );\n}\n\n\n\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;;AAZA;;;;;;;;;;;AAiCA,gDAAgD;AAChD,MAAM,yBAAyB,CAAC;IAC9B,0EAA0E;IAC1E,OAAO,GAAG,eAAe,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,KAAK;AAClE;AAEA,gDAAgD;AAChD,MAAM,wBAAwB,CAAC;IAC7B,iEAAiE;IACjE,MAAM,WAAW,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,SAAS;IAEtD,uBAAuB;IACvB,OAAO,SACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;AACV;AAEA,2BAA2B;AAC3B,SAAS,cAAc,EAAE,KAAK,EAAE,kBAAkB,EAAoD;IACpG,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAI,WAAU;sCACb,cAAA,6WAAC,+NAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAErB,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6WAAC;oCAAI,WAAU;8CACb,cAAA,6WAAC;kDAAG,MAAM,OAAO;;;;;;;;;;;8CAEnB,6WAAC;oCAAI,WAAU;8CACb,cAAA,6WAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;AAEA,oBAAoB;AACpB,MAAM,aAAa;IACjB,SAAS,6DAAmC;IAC5C,SAAS;IACT,eAAe;AACjB;AAEA,8BAA8B;AAC9B,eAAe,UAAU,QAAgB,EAAE,UAAuB,CAAC,CAAC;IAClE,IAAI,YAA0B;IAE9B,IAAK,IAAI,UAAU,GAAG,UAAU,WAAW,aAAa,EAAE,UAAW;QACnE,IAAI;YACF,MAAM,aAAa,IAAI;YACvB,MAAM,YAAY,WAAW,IAAM,WAAW,KAAK,IAAI,WAAW,OAAO;YAEzE,MAAM,WAAW,MAAM,MAAM,GAAG,WAAW,OAAO,GAAG,UAAU,EAAE;gBAC/D,GAAG,OAAO;gBACV,QAAQ,WAAW,MAAM;YAC3B;YAEA,aAAa;YAEb,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;YACnC;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,YAAY;YACZ,IAAI,iBAAiB,SAAS,MAAM,IAAI,KAAK,cAAc;gBACzD,MAAM,IAAI,MAAM;YAClB;YACA,6CAA6C;YAC7C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,KAAK,GAAG,CAAC,GAAG,WAAW;QAC1E;IACF;IAEA,MAAM,aAAa,IAAI,MAAM;AAC/B;AAEA,iCAAiC;AACjC,MAAM,kBAAkB;IACtB,2BAA2B;IAC3B,0BAA0B;IAC1B,mBAAmB;IACnB,oBAAoB;IACpB,mBAAmB;IACnB,sBAAsB;AACxB;AAEA,0BAA0B;AAC1B,MAAM,mBAAmB;IACvB,SAAS,oDAAyB;IAClC,UAAU,QAAQ,GAAG,CAAC,8BAA8B;IACpD,OAAO,QAAQ,GAAG,CAAC,kBAAkB;AACvC;AAEA,yBAAyB;AACzB,MAAM,qBAAqB;IACzB,UAAU;IACV,iBAAiB;IACjB,UAAU;IACV,QAAQ;AACV;AAEA,4BAA4B;AAC5B,MAAM,mBAAmB,CAAC;IACxB,IAAI,iBAAiB,OAAO,EAAE;QAC5B,kBAAkB,CAAC,OAAO;QAE1B,qCAAqC;QACrC,IAAI,iBAAiB,QAAQ,EAAE;YAC7B,MAAM,iBAAiB,QAAQ,EAAE;gBAC/B,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO,iBAAiB,KAAK;oBAC7B;oBACA,OAAO,kBAAkB,CAAC,OAAO;oBACjC,WAAW,IAAI,OAAO,WAAW;gBACnC;YACF,GAAG,KAAK,CAAC,QAAQ,KAAK;QACxB;IACF;AACF;AAEA,0BAA0B;AAC1B,MAAM,uBAAuB,CAAC,QAAgB;IAC5C,IAAI,iBAAiB,OAAO,EAAE;QAC5B,8CAA8C;QAC9C,IAAI,iBAAiB,QAAQ,EAAE;YAC7B,MAAM,iBAAiB,QAAQ,EAAE;gBAC/B,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO,iBAAiB,KAAK;oBAC7B;oBACA;oBACA,WAAW,IAAI,OAAO,WAAW;gBACnC;YACF,GAAG,KAAK,CAAC,QAAQ,KAAK;QACxB;IACF;AACF;AAEA,MAAM,aAAa,CAAA,GAAA,8RAAA,CAAA,UAAO,AAAD;;;;;;IACvB,KAAK;IACL,SAAS,kBAAM,6WAAC;sBAAI;;;;;;;AAGP,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,UAAU;QACV,iBAAiB;QACjB,aAAa;IACf;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QAET,IAAI,SAAS,QAAQ,KAAK,SAAS,eAAe,EAAE;YAClD,SAAS;YACT;QACF;QAEA,IAAI,CAAC,SAAS,WAAW,EAAE;YACzB,SAAS;YACT;QACF;QAEA,aAAa;QAEb,IAAI;YACF,MAAM,SAAS;YACf,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;QAC/C,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;YAC1C,CAAC;IACH;IAEA,qBACE,6WAAC;QACC,OAAM;QACN,aAAY;;YAEX,uBACC,6WAAC,0IAAA,CAAA,eAAY;gBACX,MAAK;gBACL,SAAS;gBACT,SAAS,IAAM,SAAS;;;;;;0BAI5B,6WAAC,sIAAA,CAAA,WAAQ;gBACP,OAAM;gBACN,UAAS;gBACT,UAAU;gBACV,WAAW;gBACX,YAAW;gBACX,sBACE,6WAAC;oBAAE,WAAU;;wBAAuD;wBACzC;sCACzB,6WAAC,2RAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;0BAML,cAAA,6WAAC;oBAAI,WAAU;;sCACb,6WAAC,uIAAA,CAAA,YAAS;4BACR,OAAM;4BACN,MAAK;4BACL,MAAK;4BACL,OAAO,SAAS,IAAI;4BACpB,UAAU;4BACV,QAAQ;4BACR,aAAY;;;;;;sCAGd,6WAAC,uIAAA,CAAA,YAAS;4BACR,OAAM;4BACN,MAAK;4BACL,MAAK;4BACL,OAAO,SAAS,KAAK;4BACrB,UAAU;4BACV,QAAQ;4BACR,aAAY;;;;;;sCAGd,6WAAC,uIAAA,CAAA,YAAS;4BACR,OAAM;4BACN,MAAK;4BACL,MAAK;4BACL,OAAO,SAAS,QAAQ;4BACxB,UAAU;4BACV,QAAQ;4BACR,aAAY;4BACZ,YAAW;;;;;;sCAGb,6WAAC,uIAAA,CAAA,YAAS;4BACR,OAAM;4BACN,MAAK;4BACL,MAAK;4BACL,OAAO,SAAS,eAAe;4BAC/B,UAAU;4BACV,QAAQ;4BACR,aAAY;;;;;;sCAGd,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAI,WAAU;8CACb,cAAA,6WAAC;wCACC,MAAK;wCACL,IAAG;wCACH,MAAK;wCACL,SAAS,SAAS,WAAW;wCAC7B,UAAU;wCACV,WAAU;;;;;;;;;;;8CAGd,6WAAC;oCAAI,WAAU;8CACb,cAAA,6WAAC;wCACC,SAAQ;wCACR,WAAU;;4CACX;4CACgB;0DACf,6WAAC,2RAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;4CAEO;4CAAI;4CACR;0DACJ,6WAAC,2RAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}]}