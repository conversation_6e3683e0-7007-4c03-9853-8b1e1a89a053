{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/components/dashboard/MedicalEducationDashboard.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { \r\n  Book<PERSON><PERSON>, \r\n  Clock, \r\n  TrendingUp, \r\n  Calendar, \r\n  Bell, \r\n  Search, \r\n  Plus, \r\n  Settings, \r\n  ChevronRight,\r\n  Target,\r\n  Award,\r\n  Users,\r\n  BarChart3,\r\n  CheckCircle,\r\n  XCircle,\r\n  GripVertical\r\n} from 'lucide-react';\r\nimport { LineChart, Line, AreaChart, Area, BarChart, Bar, Cell, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';\r\n\r\n// Define interfaces for type safety\r\ninterface Widget {\r\n  id: string;\r\n  x: number;\r\n  y: number;\r\n  w: number;\r\n  h: number;\r\n  component: string;\r\n}\r\n\r\ninterface StatCardProps {\r\n  icon: React.ComponentType<{ className?: string }>;\r\n  title: string;\r\n  value: string | number;\r\n  change?: number;\r\n  color: string;\r\n}\r\n\r\ninterface DashboardData {\r\n  stats: {\r\n    totalCourses: number;\r\n    completedCourses: number;\r\n    hoursStudied: number;\r\n    avgScore: number;\r\n  };\r\n  progressData: { month: string; hours: number; score: number }[];\r\n  courseData: { name: string; completed: number; total: number }[];\r\n  upcomingDeadlines: { id: number; title: string; date: string; priority: string }[];\r\n  recentActivity: { id: number; type: string; title: string; time: string }[];\r\n}\r\n\r\nexport const MedicalEducationDashboard = () => {\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [selectedFilter, setSelectedFilter] = useState('all');\r\n  const [notifications, setNotifications] = useState([]);\r\n  const [draggedWidget, setDraggedWidget] = useState<string | null>(null);\r\n  const [widgets, setWidgets] = useState<Widget[]>([\r\n    { id: 'progress', x: 0, y: 0, w: 2, h: 1, component: 'ProgressChart' },\r\n    { id: 'schedule', x: 2, y: 0, w: 1, h: 1, component: 'StudySchedule' },\r\n    { id: 'completion', x: 0, y: 1, w: 1, h: 1, component: 'CourseCompletion' },\r\n    { id: 'deadlines', x: 1, y: 1, w: 2, h: 1, component: 'UpcomingDeadlines' }\r\n  ]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  // Mock data\r\n  const [dashboardData, setDashboardData] = useState<DashboardData>({\r\n    stats: {\r\n      totalCourses: 12,\r\n      completedCourses: 8,\r\n      hoursStudied: 145,\r\n      avgScore: 87\r\n    },\r\n    progressData: [\r\n      { month: 'Jan', hours: 20, score: 85 },\r\n      { month: 'Feb', hours: 25, score: 88 },\r\n      { month: 'Mar', hours: 30, score: 87 },\r\n      { month: 'Apr', hours: 35, score: 90 },\r\n      { month: 'May', hours: 28, score: 89 },\r\n      { month: 'Jun', hours: 32, score: 92 }\r\n    ],\r\n    courseData: [\r\n      { name: 'Anatomy', completed: 95, total: 100 },\r\n      { name: 'Physiology', completed: 78, total: 100 },\r\n      { name: 'Pathology', completed: 85, total: 100 },\r\n      { name: 'Pharmacology', completed: 62, total: 100 }\r\n    ],\r\n    upcomingDeadlines: [\r\n      { id: 1, title: 'Cardiology Quiz', date: '2025-06-10', priority: 'high' },\r\n      { id: 2, title: 'Anatomy Assignment', date: '2025-06-12', priority: 'medium' },\r\n      { id: 3, title: 'Clinical Case Study', date: '2025-06-15', priority: 'low' }\r\n    ],\r\n    recentActivity: [\r\n      { id: 1, type: 'completion', title: 'Completed Respiratory System Module', time: '2 hours ago' },\r\n      { id: 2, type: 'quiz', title: 'Scored 94% on Cardiovascular Quiz', time: '1 day ago' },\r\n      { id: 3, type: 'study', title: 'Studied for 3 hours', time: '2 days ago' }\r\n    ]\r\n  });\r\n\r\n  // Simulate API fetch\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      setLoading(true);\r\n      try {\r\n        await new Promise(resolve => setTimeout(resolve, 1000));\r\n        setLoading(false);\r\n      } catch (err) {\r\n        setError('Failed to load dashboard data');\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchData();\r\n  }, []);\r\n\r\n  // Real-time updates simulation\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      setDashboardData(prev => ({\r\n        ...prev,\r\n        stats: {\r\n          ...prev.stats,\r\n          hoursStudied: prev.stats.hoursStudied + Math.floor(Math.random() * 2)\r\n        }\r\n      }));\r\n    }, 30000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, []);\r\n\r\n  const handleDragStart = (e: React.DragEvent<HTMLDivElement>, widgetId: string) => {\r\n    setDraggedWidget(widgetId);\r\n    e.dataTransfer.effectAllowed = 'move';\r\n  };\r\n\r\n  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {\r\n    e.preventDefault();\r\n    e.dataTransfer.dropEffect = 'move';\r\n  };\r\n\r\n  const handleDrop = (e: React.DragEvent<HTMLDivElement>, targetId: string) => {\r\n    e.preventDefault();\r\n    if (draggedWidget && draggedWidget !== targetId) {\r\n      setWidgets(prev => {\r\n        const newWidgets = [...prev];\r\n        const draggedIndex = newWidgets.findIndex(w => w.id === draggedWidget);\r\n        const targetIndex = newWidgets.findIndex(w => w.id === targetId);\r\n        \r\n        if (draggedIndex !== -1 && targetIndex !== -1) {\r\n          [newWidgets[draggedIndex], newWidgets[targetIndex]] = [newWidgets[targetIndex], newWidgets[draggedIndex]];\r\n        }\r\n        return newWidgets;\r\n      });\r\n    }\r\n    setDraggedWidget(null);\r\n  };\r\n\r\n  const StatCard = ({ icon: Icon, title, value, change, color }: StatCardProps) => (\r\n    <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow\">\r\n      <div className=\"flex items-center justify-between\">\r\n        <div>\r\n          <p className=\"text-sm font-medium text-gray-600\">{title}</p>\r\n          <p className=\"text-2xl font-bold text-gray-900 mt-1\">{value}</p>\r\n          {change && (\r\n            <p className={`text-sm mt-1 ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>\r\n              {change >= 0 ? '+' : ''}{change}% from last month\r\n            </p>\r\n          )}\r\n        </div>\r\n        <div className={`p-3 rounded-full ${color}`}>\r\n          <Icon className=\"w-6 h-6 text-white\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const ProgressChart = () => (\r\n    <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100\">\r\n      <div className=\"flex items-center justify-between mb-4\">\r\n        <h3 className=\"text-lg font-semibold text-gray-900\">Study Progress</h3>\r\n        <div className=\"flex space-x-2\">\r\n          <button \r\n            className=\"p-2 hover:bg-gray-100 rounded-lg\" \r\n            aria-label=\"Configure study progress chart\"\r\n          >\r\n            <Settings className=\"w-4 h-4 text-gray-500\" />\r\n          </button>\r\n        </div>\r\n      </div>\r\n      <ResponsiveContainer width=\"100%\" height={200}>\r\n        <AreaChart data={dashboardData.progressData}>\r\n          <CartesianGrid strokeDasharray=\"3 3\" />\r\n          <XAxis dataKey=\"month\" />\r\n          <YAxis />\r\n          <Tooltip />\r\n          <Area type=\"monotone\" dataKey=\"hours\" stackId=\"1\" stroke=\"#3B82F6\" fill=\"#3B82F6\" fillOpacity={0.6} />\r\n          <Area type=\"monotone\" dataKey=\"score\" stackId=\"2\" stroke=\"#10B981\" fill=\"#10B981\" fillOpacity={0.6} />\r\n        </AreaChart>\r\n      </ResponsiveContainer>\r\n    </div>\r\n  );\r\n\r\n  const CourseCompletion = () => (\r\n    <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100\">\r\n      <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Course Progress</h3>\r\n      <div className=\"space-y-4\">\r\n        {dashboardData.courseData.map((course) => (\r\n          <div key={course.name}>\r\n            <div className=\"flex justify-between text-sm mb-1\">\r\n              <span className=\"font-medium text-gray-700\">{course.name}</span>\r\n              <span className=\"text-gray-500\">{course.completed}%</span>\r\n            </div>\r\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\r\n              <div \r\n                className=\"progress-bar\"\r\n                style={{ '--progress-width': `${course.completed}%` } as React.CSSProperties}\r\n              ></div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const UpcomingDeadlines = () => (\r\n    <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100\">\r\n      <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Upcoming Deadlines</h3>\r\n      <div className=\"space-y-3\">\r\n        {dashboardData.upcomingDeadlines.map((deadline) => (\r\n          <div key={deadline.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\r\n            <div>\r\n              <p className=\"font-medium text-gray-900\">{deadline.title}</p>\r\n              <p className=\"text-sm text-gray-500\">{deadline.date}</p>\r\n            </div>\r\n            <div className={`px-2 py-1 rounded-full text-xs font-medium ${\r\n              deadline.priority === 'high' ? 'bg-red-100 text-red-800' :\r\n              deadline.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :\r\n              'bg-green-100 text-green-800'\r\n            }`}>\r\n              {deadline.priority}\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const StudySchedule = () => (\r\n    <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100\">\r\n      <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Today's Schedule</h3>\r\n      <div className=\"space-y-3\">\r\n        <div className=\"flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg\">\r\n          <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\r\n          <div>\r\n            <p className=\"font-medium text-gray-900\">Anatomy Review</p>\r\n            <p className=\"text-sm text-gray-500\">9:00 AM - 10:30 AM</p>\r\n          </div>\r\n        </div>\r\n        <div className=\"flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg\">\r\n          <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\r\n          <div>\r\n            <p className=\"font-medium text-gray-900\">Clinical Skills</p>\r\n            <p className=\"text-sm text-gray-500\">2:00 PM - 4:00 PM</p>\r\n          </div>\r\n        </div>\r\n        <div className=\"flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg\">\r\n          <div className=\"w-2 h-2 bg-purple-500 rounded-full\"></div>\r\n          <div>\r\n            <p className=\"font-medium text-gray-900\">Study Group</p>\r\n            <p className=\"text-sm text-gray-500\">7:00 PM - 9:00 PM</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderWidget = (widget: Widget) => {\r\n    const components: { [key: string]: () => JSX.Element } = {\r\n      ProgressChart,\r\n      CourseCompletion,\r\n      UpcomingDeadlines,\r\n      StudySchedule\r\n    };\r\n    \r\n    const Component = components[widget.component];\r\n    return Component ? <Component /> : null;\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <div className=\"w-4 h-4 bg-blue-600 rounded-full animate-pulse\"></div>\r\n          <div className=\"w-4 h-4 bg-blue-600 rounded-full animate-pulse delay-75\"></div>\r\n          <div className=\"w-4 h-4 bg-blue-600 rounded-full animate-pulse delay-150\"></div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\r\n        <div className=\"text-center p-6 bg-white rounded-xl shadow-sm border border-red-200\">\r\n          <XCircle className=\"w-12 h-12 text-red-500 mx-auto mb-4\" />\r\n          <p className=\"text-red-600 font-medium\">{error}</p>\r\n          <button \r\n            onClick={() => window.location.reload()} \r\n            className=\"mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700\"\r\n          >\r\n            Retry\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      <header className=\"bg-white border-b border-gray-200 px-6 py-4\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <h1 className=\"text-2xl font-bold text-gray-900\">Medical Education Dashboard</h1>\r\n            <p className=\"text-gray-600\">Track your learning progress and stay organized</p>\r\n          </div>\r\n          <div className=\"flex items-center space-x-4\">\r\n            <div className=\"relative\">\r\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Search courses, assignments...\"\r\n                value={searchTerm}\r\n                onChange={(e) => setSearchTerm(e.target.value)}\r\n                className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n              />\r\n            </div>\r\n            <div className=\"relative\">\r\n              <label htmlFor=\"course-filter\" className=\"sr-only\">Filter courses</label>\r\n              <select \r\n                id=\"course-filter\"\r\n                value={selectedFilter}\r\n                onChange={(e) => setSelectedFilter(e.target.value)}\r\n                className=\"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500\"\r\n              >\r\n                <option value=\"all\">All Courses</option>\r\n                <option value=\"active\">Active</option>\r\n                <option value=\"completed\">Completed</option>\r\n              </select>\r\n            </div>\r\n            <button \r\n              className=\"relative p-2 text-gray-600 hover:text-gray-900\"\r\n              aria-label=\"View notifications\"\r\n            >\r\n              <Bell className=\"w-5 h-5\" />\r\n              <span className=\"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full\"></span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      <div className=\"flex\">\r\n        <aside className=\"w-64 bg-white border-r border-gray-200 min-h-screen p-6\">\r\n          <nav className=\"space-y-2\">\r\n            <a href=\"#\" className=\"flex items-center space-x-3 px-3 py-2 text-blue-600 bg-blue-50 rounded-lg\">\r\n              <BarChart3 className=\"w-5 h-5\" />\r\n              <span className=\"font-medium\">Dashboard</span>\r\n            </a>\r\n            <a href=\"#\" className=\"flex items-center space-x-3 px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-lg\">\r\n              <BookOpen className=\"w-5 h-5\" />\r\n              <span>My Courses</span>\r\n            </a>\r\n            <a href=\"#\" className=\"flex items-center space-x-3 px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-lg\">\r\n              <Calendar className=\"w-5 h-5\" />\r\n              <span>Schedule</span>\r\n            </a>\r\n            <a href=\"#\" className=\"flex items-center space-x-3 px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-lg\">\r\n              <Target className=\"w-5 h-5\" />\r\n              <span>Goals</span>\r\n            </a>\r\n            <a href=\"#\" className=\"flex items-center space-x-3 px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-lg\">\r\n              <Award className=\"w-5 h-5\" />\r\n              <span>Achievements</span>\r\n            </a>\r\n            <a href=\"#\" className=\"flex items-center space-x-3 px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-lg\">\r\n              <Users className=\"w-5 h-5\" />\r\n              <span>Study Groups</span>\r\n            </a>\r\n          </nav>\r\n\r\n          <div className=\"mt-8\">\r\n            <h3 className=\"text-sm font-semibold text-gray-900 mb-3\">Quick Actions</h3>\r\n            <div className=\"space-y-2\">\r\n              <button className=\"w-full flex items-center space-x-2 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg\">\r\n                <Plus className=\"w-4 h-4\" />\r\n                <span>Add Course</span>\r\n              </button>\r\n              <button className=\"w-full flex items-center space-x-2 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg\">\r\n                <Calendar className=\"w-4 h-4\" />\r\n                <span>Schedule Study</span>\r\n              </button>\r\n              <button className=\"w-full flex items-center space-x-2 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg\">\r\n                <Target className=\"w-4 h-4\" />\r\n                <span>Set Goal</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </aside>\r\n\r\n        <main className=\"flex-1 p-6\">\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\r\n            <StatCard\r\n              icon={BookOpen}\r\n              title=\"Total Courses\"\r\n              value={dashboardData.stats.totalCourses}\r\n              change={8}\r\n              color=\"bg-blue-500\"\r\n            />\r\n            <StatCard\r\n              icon={CheckCircle}\r\n              title=\"Completed\"\r\n              value={dashboardData.stats.completedCourses}\r\n              change={12}\r\n              color=\"bg-green-500\"\r\n            />\r\n            <StatCard\r\n              icon={Clock}\r\n              title=\"Hours Studied\"\r\n              value={dashboardData.stats.hoursStudied}\r\n              change={15}\r\n              color=\"bg-purple-500\"\r\n            />\r\n            <StatCard\r\n              icon={TrendingUp}\r\n              title=\"Average Score\"\r\n              value={`${dashboardData.stats.avgScore}%`}\r\n              change={5}\r\n              color=\"bg-orange-500\"\r\n            />\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8\">\r\n            {widgets.map((widget) => (\r\n              <div\r\n                key={widget.id}\r\n                draggable\r\n                onDragStart={(e) => handleDragStart(e, widget.id)}\r\n                onDragOver={handleDragOver}\r\n                onDrop={(e) => handleDrop(e, widget.id)}\r\n                className=\"cursor-move relative group\"\r\n              >\r\n                <div className=\"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity\">\r\n                  <GripVertical className=\"w-4 h-4 text-gray-400\" />\r\n                </div>\r\n                {renderWidget(widget)}\r\n              </div>\r\n            ))}\r\n          </div>\r\n\r\n          <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100\">\r\n            <div className=\"flex items-center justify-between mb-6\">\r\n              <h3 className=\"text-lg font-semibold text-gray-900\">Recent Activity</h3>\r\n              <button className=\"text-blue-600 hover:text-blue-700 font-medium\">View All</button>\r\n            </div>\r\n            <div className=\"space-y-4\">\r\n              {dashboardData.recentActivity.map((activity) => (\r\n                <div key={activity.id} className=\"flex items-center space-x-4 p-4 hover:bg-gray-50 rounded-lg\">\r\n                  <div className={`p-2 rounded-full ${\r\n                    activity.type === 'completion' ? 'bg-green-100' :\r\n                    activity.type === 'quiz' ? 'bg-blue-100' : 'bg-purple-100'\r\n                  }`}>\r\n                    {activity.type === 'completion' ? (\r\n                      <CheckCircle className=\"w-4 h-4 text-green-600\" />\r\n                    ) : activity.type === 'quiz' ? (\r\n                      <Award className=\"w-4 h-4 text-blue-600\" />\r\n                    ) : (\r\n                      <BookOpen className=\"w-4 h-4 text-purple-600\" />\r\n                    )}\r\n                  </div>\r\n                  <div className=\"flex-1\">\r\n                    <p className=\"font-medium text-gray-900\">{activity.title}</p>\r\n                    <p className=\"text-sm text-gray-500\">{activity.time}</p>\r\n                  </div>\r\n                  <ChevronRight className=\"w-4 h-4 text-gray-400\" />\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AArBA;;;;;AAsDO,MAAM,4BAA4B;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAY;QAC/C;YAAE,IAAI;YAAY,GAAG;YAAG,GAAG;YAAG,GAAG;YAAG,GAAG;YAAG,WAAW;QAAgB;QACrE;YAAE,IAAI;YAAY,GAAG;YAAG,GAAG;YAAG,GAAG;YAAG,GAAG;YAAG,WAAW;QAAgB;QACrE;YAAE,IAAI;YAAc,GAAG;YAAG,GAAG;YAAG,GAAG;YAAG,GAAG;YAAG,WAAW;QAAmB;QAC1E;YAAE,IAAI;YAAa,GAAG;YAAG,GAAG;YAAG,GAAG;YAAG,GAAG;YAAG,WAAW;QAAoB;KAC3E;IACD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,YAAY;IACZ,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiB;QAChE,OAAO;YACL,cAAc;YACd,kBAAkB;YAClB,cAAc;YACd,UAAU;QACZ;QACA,cAAc;YACZ;gBAAE,OAAO;gBAAO,OAAO;gBAAI,OAAO;YAAG;YACrC;gBAAE,OAAO;gBAAO,OAAO;gBAAI,OAAO;YAAG;YACrC;gBAAE,OAAO;gBAAO,OAAO;gBAAI,OAAO;YAAG;YACrC;gBAAE,OAAO;gBAAO,OAAO;gBAAI,OAAO;YAAG;YACrC;gBAAE,OAAO;gBAAO,OAAO;gBAAI,OAAO;YAAG;YACrC;gBAAE,OAAO;gBAAO,OAAO;gBAAI,OAAO;YAAG;SACtC;QACD,YAAY;YACV;gBAAE,MAAM;gBAAW,WAAW;gBAAI,OAAO;YAAI;YAC7C;gBAAE,MAAM;gBAAc,WAAW;gBAAI,OAAO;YAAI;YAChD;gBAAE,MAAM;gBAAa,WAAW;gBAAI,OAAO;YAAI;YAC/C;gBAAE,MAAM;gBAAgB,WAAW;gBAAI,OAAO;YAAI;SACnD;QACD,mBAAmB;YACjB;gBAAE,IAAI;gBAAG,OAAO;gBAAmB,MAAM;gBAAc,UAAU;YAAO;YACxE;gBAAE,IAAI;gBAAG,OAAO;gBAAsB,MAAM;gBAAc,UAAU;YAAS;YAC7E;gBAAE,IAAI;gBAAG,OAAO;gBAAuB,MAAM;gBAAc,UAAU;YAAM;SAC5E;QACD,gBAAgB;YACd;gBAAE,IAAI;gBAAG,MAAM;gBAAc,OAAO;gBAAuC,MAAM;YAAc;YAC/F;gBAAE,IAAI;gBAAG,MAAM;gBAAQ,OAAO;gBAAqC,MAAM;YAAY;YACrF;gBAAE,IAAI;gBAAG,MAAM;gBAAS,OAAO;gBAAuB,MAAM;YAAa;SAC1E;IACH;IAEA,qBAAqB;IACrB,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,WAAW;YACX,IAAI;gBACF,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD,WAAW;YACb,EAAE,OAAO,KAAK;gBACZ,SAAS;gBACT,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,+BAA+B;IAC/B,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,iBAAiB,CAAA,OAAQ,CAAC;oBACxB,GAAG,IAAI;oBACP,OAAO;wBACL,GAAG,KAAK,KAAK;wBACb,cAAc,KAAK,KAAK,CAAC,YAAY,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;oBACrE;gBACF,CAAC;QACH,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC,GAAoC;QAC3D,iBAAiB;QACjB,EAAE,YAAY,CAAC,aAAa,GAAG;IACjC;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,EAAE,YAAY,CAAC,UAAU,GAAG;IAC9B;IAEA,MAAM,aAAa,CAAC,GAAoC;QACtD,EAAE,cAAc;QAChB,IAAI,iBAAiB,kBAAkB,UAAU;YAC/C,WAAW,CAAA;gBACT,MAAM,aAAa;uBAAI;iBAAK;gBAC5B,MAAM,eAAe,WAAW,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACxD,MAAM,cAAc,WAAW,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAEvD,IAAI,iBAAiB,CAAC,KAAK,gBAAgB,CAAC,GAAG;oBAC7C,CAAC,UAAU,CAAC,aAAa,EAAE,UAAU,CAAC,YAAY,CAAC,GAAG;wBAAC,UAAU,CAAC,YAAY;wBAAE,UAAU,CAAC,aAAa;qBAAC;gBAC3G;gBACA,OAAO;YACT;QACF;QACA,iBAAiB;IACnB;IAEA,MAAM,WAAW,CAAC,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAiB,iBAC1E,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;;0CACC,6WAAC;gCAAE,WAAU;0CAAqC;;;;;;0CAClD,6WAAC;gCAAE,WAAU;0CAAyC;;;;;;4BACrD,wBACC,6WAAC;gCAAE,WAAW,CAAC,aAAa,EAAE,UAAU,IAAI,mBAAmB,gBAAgB;;oCAC5E,UAAU,IAAI,MAAM;oCAAI;oCAAO;;;;;;;;;;;;;kCAItC,6WAAC;wBAAI,WAAW,CAAC,iBAAiB,EAAE,OAAO;kCACzC,cAAA,6WAAC;4BAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;IAMxB,MAAM,gBAAgB,kBACpB,6WAAC;YAAI,WAAU;;8BACb,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,6WAAC;4BAAI,WAAU;sCACb,cAAA,6WAAC;gCACC,WAAU;gCACV,cAAW;0CAEX,cAAA,6WAAC,8RAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8BAI1B,6WAAC,sSAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAO,QAAQ;8BACxC,cAAA,6WAAC,wRAAA,CAAA,YAAS;wBAAC,MAAM,cAAc,YAAY;;0CACzC,6WAAC,gSAAA,CAAA,gBAAa;gCAAC,iBAAgB;;;;;;0CAC/B,6WAAC,wRAAA,CAAA,QAAK;gCAAC,SAAQ;;;;;;0CACf,6WAAC,wRAAA,CAAA,QAAK;;;;;0CACN,6WAAC,0RAAA,CAAA,UAAO;;;;;0CACR,6WAAC,uRAAA,CAAA,OAAI;gCAAC,MAAK;gCAAW,SAAQ;gCAAQ,SAAQ;gCAAI,QAAO;gCAAU,MAAK;gCAAU,aAAa;;;;;;0CAC/F,6WAAC,uRAAA,CAAA,OAAI;gCAAC,MAAK;gCAAW,SAAQ;gCAAQ,SAAQ;gCAAI,QAAO;gCAAU,MAAK;gCAAU,aAAa;;;;;;;;;;;;;;;;;;;;;;;IAMvG,MAAM,mBAAmB,kBACvB,6WAAC;YAAI,WAAU;;8BACb,6WAAC;oBAAG,WAAU;8BAA2C;;;;;;8BACzD,6WAAC;oBAAI,WAAU;8BACZ,cAAc,UAAU,CAAC,GAAG,CAAC,CAAC,uBAC7B,6WAAC;;8CACC,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAK,WAAU;sDAA6B,OAAO,IAAI;;;;;;sDACxD,6WAAC;4CAAK,WAAU;;gDAAiB,OAAO,SAAS;gDAAC;;;;;;;;;;;;;8CAEpD,6WAAC;oCAAI,WAAU;8CACb,cAAA,6WAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,oBAAoB,GAAG,OAAO,SAAS,CAAC,CAAC,CAAC;wCAAC;;;;;;;;;;;;2BARhD,OAAO,IAAI;;;;;;;;;;;;;;;;IAiB7B,MAAM,oBAAoB,kBACxB,6WAAC;YAAI,WAAU;;8BACb,6WAAC;oBAAG,WAAU;8BAA2C;;;;;;8BACzD,6WAAC;oBAAI,WAAU;8BACZ,cAAc,iBAAiB,CAAC,GAAG,CAAC,CAAC,yBACpC,6WAAC;4BAAsB,WAAU;;8CAC/B,6WAAC;;sDACC,6WAAC;4CAAE,WAAU;sDAA6B,SAAS,KAAK;;;;;;sDACxD,6WAAC;4CAAE,WAAU;sDAAyB,SAAS,IAAI;;;;;;;;;;;;8CAErD,6WAAC;oCAAI,WAAW,CAAC,2CAA2C,EAC1D,SAAS,QAAQ,KAAK,SAAS,4BAC/B,SAAS,QAAQ,KAAK,WAAW,kCACjC,+BACA;8CACC,SAAS,QAAQ;;;;;;;2BAVZ,SAAS,EAAE;;;;;;;;;;;;;;;;IAkB7B,MAAM,gBAAgB,kBACpB,6WAAC;YAAI,WAAU;;8BACb,6WAAC;oBAAG,WAAU;8BAA2C;;;;;;8BACzD,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAI,WAAU;;;;;;8CACf,6WAAC;;sDACC,6WAAC;4CAAE,WAAU;sDAA4B;;;;;;sDACzC,6WAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAGzC,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAI,WAAU;;;;;;8CACf,6WAAC;;sDACC,6WAAC;4CAAE,WAAU;sDAA4B;;;;;;sDACzC,6WAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAGzC,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAI,WAAU;;;;;;8CACf,6WAAC;;sDACC,6WAAC;4CAAE,WAAU;sDAA4B;;;;;;sDACzC,6WAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAO/C,MAAM,eAAe,CAAC;QACpB,MAAM,aAAmD;YACvD;YACA;YACA;YACA;QACF;QAEA,MAAM,YAAY,UAAU,CAAC,OAAO,SAAS,CAAC;QAC9C,OAAO,0BAAY,6WAAC;;;;uDAAe;IACrC;IAEA,IAAI,SAAS;QACX,qBACE,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAU;;;;;;kCACf,6WAAC;wBAAI,WAAU;;;;;;kCACf,6WAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,IAAI,OAAO;QACT,qBACE,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAU;;kCACb,6WAAC,gSAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6WAAC;wBAAE,WAAU;kCAA4B;;;;;;kCACzC,6WAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BACb,6WAAC;gBAAO,WAAU;0BAChB,cAAA,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;;8CACC,6WAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6WAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAE/B,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,0RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6WAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;8CAGd,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAM,SAAQ;4CAAgB,WAAU;sDAAU;;;;;;sDACnD,6WAAC;4CACC,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4CACjD,WAAU;;8DAEV,6WAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6WAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6WAAC;oDAAO,OAAM;8DAAY;;;;;;;;;;;;;;;;;;8CAG9B,6WAAC;oCACC,WAAU;oCACV,cAAW;;sDAEX,6WAAC,sRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6WAAC;4CAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMxB,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAM,WAAU;;0CACf,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAE,MAAK;wCAAI,WAAU;;0DACpB,6WAAC,wSAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6WAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAEhC,6WAAC;wCAAE,MAAK;wCAAI,WAAU;;0DACpB,6WAAC,kSAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6WAAC;0DAAK;;;;;;;;;;;;kDAER,6WAAC;wCAAE,MAAK;wCAAI,WAAU;;0DACpB,6WAAC,8RAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6WAAC;0DAAK;;;;;;;;;;;;kDAER,6WAAC;wCAAE,MAAK;wCAAI,WAAU;;0DACpB,6WAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6WAAC;0DAAK;;;;;;;;;;;;kDAER,6WAAC;wCAAE,MAAK;wCAAI,WAAU;;0DACpB,6WAAC,wRAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6WAAC;0DAAK;;;;;;;;;;;;kDAER,6WAAC;wCAAE,MAAK;wCAAI,WAAU;;0DACpB,6WAAC,wRAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6WAAC;0DAAK;;;;;;;;;;;;;;;;;;0CAIV,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAO,WAAU;;kEAChB,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6WAAC;kEAAK;;;;;;;;;;;;0DAER,6WAAC;gDAAO,WAAU;;kEAChB,6WAAC,8RAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6WAAC;kEAAK;;;;;;;;;;;;0DAER,6WAAC;gDAAO,WAAU;;kEAChB,6WAAC,0RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6WAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMd,6WAAC;wBAAK,WAAU;;0CACd,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCACC,MAAM,kSAAA,CAAA,WAAQ;wCACd,OAAM;wCACN,OAAO,cAAc,KAAK,CAAC,YAAY;wCACvC,QAAQ;wCACR,OAAM;;;;;;kDAER,6WAAC;wCACC,MAAM,wSAAA,CAAA,cAAW;wCACjB,OAAM;wCACN,OAAO,cAAc,KAAK,CAAC,gBAAgB;wCAC3C,QAAQ;wCACR,OAAM;;;;;;kDAER,6WAAC;wCACC,MAAM,wRAAA,CAAA,QAAK;wCACX,OAAM;wCACN,OAAO,cAAc,KAAK,CAAC,YAAY;wCACvC,QAAQ;wCACR,OAAM;;;;;;kDAER,6WAAC;wCACC,MAAM,sSAAA,CAAA,aAAU;wCAChB,OAAM;wCACN,OAAO,GAAG,cAAc,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;wCACzC,QAAQ;wCACR,OAAM;;;;;;;;;;;;0CAIV,6WAAC;gCAAI,WAAU;0CACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6WAAC;wCAEC,SAAS;wCACT,aAAa,CAAC,IAAM,gBAAgB,GAAG,OAAO,EAAE;wCAChD,YAAY;wCACZ,QAAQ,CAAC,IAAM,WAAW,GAAG,OAAO,EAAE;wCACtC,WAAU;;0DAEV,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC,0SAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;4CAEzB,aAAa;;uCAVT,OAAO,EAAE;;;;;;;;;;0CAepB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,6WAAC;gDAAO,WAAU;0DAAgD;;;;;;;;;;;;kDAEpE,6WAAC;wCAAI,WAAU;kDACZ,cAAc,cAAc,CAAC,GAAG,CAAC,CAAC,yBACjC,6WAAC;gDAAsB,WAAU;;kEAC/B,6WAAC;wDAAI,WAAW,CAAC,iBAAiB,EAChC,SAAS,IAAI,KAAK,eAAe,iBACjC,SAAS,IAAI,KAAK,SAAS,gBAAgB,iBAC3C;kEACC,SAAS,IAAI,KAAK,6BACjB,6WAAC,wSAAA,CAAA,cAAW;4DAAC,WAAU;;;;;uGACrB,SAAS,IAAI,KAAK,uBACpB,6WAAC,wRAAA,CAAA,QAAK;4DAAC,WAAU;;;;;qHAEjB,6WAAC,kSAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAGxB,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;gEAAE,WAAU;0EAA6B,SAAS,KAAK;;;;;;0EACxD,6WAAC;gEAAE,WAAU;0EAAyB,SAAS,IAAI;;;;;;;;;;;;kEAErD,6WAAC,0SAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;+CAjBhB,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BrC", "debugId": null}}]}