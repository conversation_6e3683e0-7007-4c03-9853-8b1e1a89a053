"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIRecommendationController = void 0;
const common_1 = require("@nestjs/common");
const ai_recommendation_service_1 = require("./ai-recommendation.service");
const jwt_auth_guard_1 = require("../auth/jwt-auth.guard");
const roles_guards_1 = require("../../common/guards/roles.guards");
let AIRecommendationController = class AIRecommendationController {
    constructor(aiRecommendationService) {
        this.aiRecommendationService = aiRecommendationService;
    }
};
exports.AIRecommendationController = AIRecommendationController;
exports.AIRecommendationController = AIRecommendationController = __decorate([
    (0, common_1.Controller)('ai-recommendations'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guards_1.RolesGuard),
    __metadata("design:paramtypes", [ai_recommendation_service_1.AIRecommendationService])
], AIRecommendationController);
//# sourceMappingURL=ai-recommendation.controller.js.map