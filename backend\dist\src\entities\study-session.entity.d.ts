import { User } from './user.entity';
import { Topic } from './topic.entity';
export declare class StudySession {
    id: string;
    user: User;
    topic: Topic;
    duration_minutes: number;
    activities: {
        type: string;
        duration_minutes: number;
        score?: number;
    }[];
    focus_score: number;
    notes: string;
    is_offline: boolean;
    started_at: Date;
    ended_at: Date;
    created_at: Date;
    updated_at: Date;
}
