import { AnalyticsService } from './analytics.service';
export declare class AnalyticsController {
    private readonly analyticsService;
    constructor(analyticsService: AnalyticsService);
    getLearningPatterns(userId: string): Promise<any>;
    getRecommendations(userId: string): Promise<any>;
    getPerformanceMetrics(userId: string): Promise<any>;
    getPeerBenchmarks(userId: string): Promise<any>;
}
