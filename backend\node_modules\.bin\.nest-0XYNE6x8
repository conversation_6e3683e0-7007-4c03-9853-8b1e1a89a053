#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@nestjs+cli@11.0.6_@swc+cli_63762b8d6733f032d60e64f25107012b/node_modules/@nestjs/cli/bin/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@nestjs+cli@11.0.6_@swc+cli_63762b8d6733f032d60e64f25107012b/node_modules/@nestjs/cli/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@nestjs+cli@11.0.6_@swc+cli_63762b8d6733f032d60e64f25107012b/node_modules/@nestjs/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@nestjs+cli@11.0.6_@swc+cli_63762b8d6733f032d60e64f25107012b/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@nestjs+cli@11.0.6_@swc+cli_63762b8d6733f032d60e64f25107012b/node_modules/@nestjs/cli/bin/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@nestjs+cli@11.0.6_@swc+cli_63762b8d6733f032d60e64f25107012b/node_modules/@nestjs/cli/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@nestjs+cli@11.0.6_@swc+cli_63762b8d6733f032d60e64f25107012b/node_modules/@nestjs/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@nestjs+cli@11.0.6_@swc+cli_63762b8d6733f032d60e64f25107012b/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@nestjs/cli/bin/nest.js" "$@"
else
  exec node  "$basedir/../@nestjs/cli/bin/nest.js" "$@"
fi
