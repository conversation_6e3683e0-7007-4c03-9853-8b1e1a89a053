"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.maxPool3DGradConfig = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var nodejs_kernel_backend_1 = require("../nodejs_kernel_backend");
exports.maxPool3DGradConfig = {
    kernelName: tfjs_1.MaxPool3DGrad,
    backendName: 'tensorflow',
    kernelFunc: function (args) {
        var _a = args.inputs, dy = _a.dy, input = _a.input, output = _a.output;
        var backend = args.backend;
        var _b = args.attrs, filterSize = _b.filterSize, strides = _b.strides, pad = _b.pad, dimRoundingMode = _b.dimRoundingMode;
        var convInfo = tfjs_1.backend_util.computePool3DInfo(input.shape, filterSize, strides, 1 /* dilations */, pad, dimRoundingMode);
        if (convInfo.padInfo.type !== 'VALID' && convInfo.padInfo.type !== 'SAME') {
            throw new Error("TF Backend supports only 'valid' and 'same' padding " +
                "while padding type was ".concat(convInfo.padInfo.type));
        }
        var ksize = [
            1, convInfo.filterDepth, convInfo.filterHeight, convInfo.filterWidth, 1
        ];
        var $strides = [
            1, convInfo.strideDepth, convInfo.strideHeight, convInfo.strideWidth, 1
        ];
        var padding = convInfo.padInfo.type;
        var dataFormat = convInfo.dataFormat === 'channelsLast' ? 'NDHWC' : 'NCDHW';
        var opAttrs = [
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('T', input.dtype),
            { name: 'ksize', type: backend.binding.TF_ATTR_INT, value: ksize },
            { name: 'strides', type: backend.binding.TF_ATTR_INT, value: $strides },
            { name: 'padding', type: backend.binding.TF_ATTR_STRING, value: padding },
            {
                name: 'data_format',
                type: backend.binding.TF_ATTR_STRING,
                value: dataFormat
            },
        ];
        return backend.executeSingleOutput(tfjs_1.MaxPool3DGrad, opAttrs, [input, output, dy]);
    }
};
