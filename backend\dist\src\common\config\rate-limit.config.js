"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getRateLimitKey = exports.rateLimitConfig = void 0;
exports.rateLimitConfig = {
    throttlers: [
        {
            name: 'default',
            ttl: 60,
            limit: 10,
        },
        {
            name: 'auth',
            ttl: 300,
            limit: 5,
        },
        {
            name: 'api',
            ttl: 60,
            limit: 60,
        },
        {
            name: 'sensitive',
            ttl: 3600,
            limit: 10,
        },
    ],
};
const getRateLimitKey = (req) => {
    const key = req.ip;
    if (req.user?.id) {
        return `${key}-${req.user.id}`;
    }
    return key;
};
exports.getRateLimitKey = getRateLimitKey;
//# sourceMappingURL=rate-limit.config.js.map