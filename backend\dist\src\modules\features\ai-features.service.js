"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AIFeaturesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIFeaturesService = void 0;
const common_1 = require("@nestjs/common");
const redis_service_1 = require("../redis/redis.service");
const user_features_service_1 = require("./user-features.service");
let AIFeaturesService = AIFeaturesService_1 = class AIFeaturesService {
    constructor(redisService, userFeaturesService) {
        this.redisService = redisService;
        this.userFeaturesService = userFeaturesService;
        this.logger = new common_1.Logger(AIFeaturesService_1.name);
        this.CACHE_TTL = 1800;
        this.CONTENT_FEATURES_PREFIX = 'content_features:';
        this.MODEL_CACHE_PREFIX = 'ai_model:';
    }
    async extractContentFeatures(contentId, contentData) {
        try {
            const cacheKey = `${this.CONTENT_FEATURES_PREFIX}${contentId}`;
            const cached = await this.redisService.get(cacheKey);
            if (cached) {
                return JSON.parse(cached);
            }
            const features = await this.generateContentFeatures(contentId, contentData);
            await this.redisService.set(cacheKey, JSON.stringify(features), this.CACHE_TTL * 4);
            return features;
        }
        catch (error) {
            this.logger.error(`Failed to extract content features for ${contentId}:`, error);
            throw error;
        }
    }
    async generateAIInput(userId, candidateContentIds, context) {
        try {
            const userFeatures = await this.userFeaturesService.getUserFeatureVector(userId);
            if (!userFeatures) {
                throw new Error(`Unable to generate user features for ${userId}`);
            }
            const contentFeatures = await Promise.all(candidateContentIds.map(async (contentId) => {
                const contentData = await this.getContentData(contentId);
                return this.extractContentFeatures(contentId, contentData);
            }));
            const contextFeatures = this.generateContextFeatures(userId, context);
            return {
                userId,
                userFeatures,
                contentFeatures,
                contextFeatures,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`Failed to generate AI input for user ${userId}:`, error);
            throw error;
        }
    }
    async createFeatureMatrix(userIds, contentIds, context) {
        try {
            const userFeatureMatrix = await Promise.all(userIds.map(async (userId) => {
                const features = await this.userFeaturesService.getUserFeatureVector(userId);
                return features || new Array(17).fill(0);
            }));
            const contentFeatureMatrix = await Promise.all(contentIds.map(async (contentId) => {
                const contentData = await this.getContentData(contentId);
                const features = await this.extractContentFeatures(contentId, contentData);
                return this.contentFeaturesToVector(features);
            }));
            const contextFeatureMatrix = userIds.map(userId => this.generateContextFeatures(userId, context));
            return {
                userFeatureMatrix,
                contentFeatureMatrix,
                contextFeatureMatrix,
                userIds,
                contentIds,
            };
        }
        catch (error) {
            this.logger.error('Failed to create feature matrix:', error);
            throw error;
        }
    }
    async predictRecommendations(input) {
        try {
            const cacheKey = `${this.MODEL_CACHE_PREFIX}${input.userId}:${input.contentFeatures.map(c => c.contentId).join(',')}`;
            const cached = await this.redisService.get(cacheKey);
            if (cached) {
                return JSON.parse(cached);
            }
            const predictions = await this.simulateMLPredictions(input);
            await this.redisService.set(cacheKey, JSON.stringify(predictions), this.CACHE_TTL);
            return predictions;
        }
        catch (error) {
            this.logger.error('Failed to generate predictions:', error);
            throw error;
        }
    }
    async analyzeFeatureImportance(userId, contentIds) {
        try {
            const input = await this.generateAIInput(userId, contentIds);
            const userFeatures = input.userFeatures.map((value, index) => ({
                featureName: this.getUserFeatureName(index),
                importance: Math.abs(value),
                category: 'user',
            }));
            const contentFeatures = input.contentFeatures.flatMap(content => this.contentFeaturesToVector(content).map((value, index) => ({
                featureName: this.getContentFeatureName(index),
                importance: Math.abs(value),
                category: 'content',
            })));
            const contextFeatures = input.contextFeatures.map((value, index) => ({
                featureName: this.getContextFeatureName(index),
                importance: Math.abs(value),
                category: 'context',
            }));
            return [...userFeatures, ...contentFeatures, ...contextFeatures]
                .sort((a, b) => b.importance - a.importance);
        }
        catch (error) {
            this.logger.error('Failed to analyze feature importance:', error);
            throw error;
        }
    }
    async getContentData(contentId) {
        return {
            id: contentId,
            type: 'article',
            difficulty: 0.5,
            topics: ['math', 'science'],
            duration: 30,
            metadata: {
                device: 'desktop',
                topic: 'algebra',
            },
        };
    }
    async generateContentFeatures(contentId, contentData) {
        return {
            contentId,
            contentType: contentData.type,
            difficulty: contentData.difficulty,
            topics: contentData.topics,
            duration: contentData.duration,
            engagement: 0.7,
            completionRate: 0.8,
            averageScore: 0.75,
            prerequisites: [],
            learningObjectives: [],
            metadata: contentData.metadata,
        };
    }
    generateContextFeatures(userId, context) {
        return [
            context?.timeOfDay || 0.5,
            context?.dayOfWeek || 0.5,
            context?.deviceType || 0.5,
            context?.location || 0.5,
        ];
    }
    contentFeaturesToVector(features) {
        return [
            features.difficulty,
            features.duration / 3600,
            features.engagement,
            features.completionRate,
            features.averageScore,
            features.topics.length / 10,
            features.prerequisites.length / 5,
            features.learningObjectives.length / 5,
        ];
    }
    async simulateMLPredictions(input) {
        return input.contentFeatures.map(content => ({
            contentId: content.contentId,
            score: Math.random(),
            confidence: 0.8 + Math.random() * 0.2,
            reasoning: [
                'Based on user learning style',
                'Matches user difficulty preference',
                'Relevant to user interests',
            ],
            category: content.contentType,
        }));
    }
    getUserFeatureName(index) {
        const names = [
            'Session Duration',
            'Learning Velocity',
            'Difficulty Progression',
            'Engagement Score',
            'Time Slot Preference',
            'Weak Areas Count',
            'Strong Areas Count',
            'Total Sessions',
            'Average Score',
            'Completion Rate',
            'Retention Rate',
            'Streak Days',
            'Days Since Last Active',
            'Social Engagement',
            'Help Seeking',
            'Top Preference Score',
            'Preference Diversity',
        ];
        return names[index] || `User Feature ${index}`;
    }
    getContentFeatureName(index) {
        const names = [
            'Difficulty',
            'Duration',
            'Engagement',
            'Completion Rate',
            'Average Score',
            'Topic Count',
            'Prerequisite Count',
            'Learning Objective Count',
        ];
        return names[index] || `Content Feature ${index}`;
    }
    getContextFeatureName(index) {
        const names = [
            'Time of Day',
            'Day of Week',
            'Device Type',
            'Location',
        ];
        return names[index] || `Context Feature ${index}`;
    }
};
exports.AIFeaturesService = AIFeaturesService;
exports.AIFeaturesService = AIFeaturesService = AIFeaturesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [redis_service_1.RedisService,
        user_features_service_1.UserFeaturesService])
], AIFeaturesService);
//# sourceMappingURL=ai-features.service.js.map