"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CPDController = void 0;
const common_1 = require("@nestjs/common");
const cpd_service_1 = require("./cpd.service");
const jwt_auth_guard_1 = require("../auth/jwt-auth.guard");
const roles_guards_1 = require("../../common/guards/roles.guards");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
let CPDController = class CPDController {
    constructor(cpdService) {
        this.cpdService = cpdService;
    }
    async verifyActivity(activityId, data) {
        return this.cpdService.verifyCPDActivity(activityId, data.verified, data.notes);
    }
    async updateCPDCycle(req, data) {
        return this.cpdService.updateCPDCycle(req.params.cycleId, data);
    }
};
exports.CPDController = CPDController;
__decorate([
    (0, common_1.Put)('activities/:id/verify'),
    (0, roles_decorator_1.Roles)('admin'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CPDController.prototype, "verifyActivity", null);
__decorate([
    (0, common_1.Post)('cycles/:cycleId'),
    (0, roles_decorator_1.Roles)('admin'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], CPDController.prototype, "updateCPDCycle", null);
exports.CPDController = CPDController = __decorate([
    (0, common_1.Controller)('cpd'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guards_1.RolesGuard),
    __metadata("design:paramtypes", [cpd_service_1.CPDService])
], CPDController);
//# sourceMappingURL=cpd.controller.js.map