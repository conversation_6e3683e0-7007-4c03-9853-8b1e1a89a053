interface ModuleEndpoint {
    path: string;
    method: string;
    description: string;
}
interface ModuleInfo {
    name: string;
    description: string;
    endpoints: ModuleEndpoint[];
    dependencies: string[];
}
interface ModuleError {
    error: string;
}
export declare class TestController {
    getTestInfo(): {
        status: string;
        message: string;
        timestamp: string;
    };
    getModules(): {
        modules: {
            name: string;
            status: string;
            endpoints: string[];
        }[];
    };
    getModuleInfo(name: string): ModuleInfo | ModuleError;
    checkAuth(): {
        authenticated: boolean;
        timestamp: string;
    };
    echo(body: any): {
        received: any;
        timestamp: string;
    };
}
export {};
