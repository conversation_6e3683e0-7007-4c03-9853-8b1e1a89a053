export declare class MetricsService {
    private register;
    private requestCounter;
    private errorCounter;
    private responseTimeHistogram;
    private activeConnections;
    private memoryUsage;
    constructor();
    incrementRequestCounter(method: string, path: string, status: number): void;
    incrementErrorCounter(method: string, path: string, status: number, error: string): void;
    recordResponseTime(method: string, path: string, time: number): void;
    setActiveConnections(count: number): void;
    getMetrics(): Promise<string>;
}
