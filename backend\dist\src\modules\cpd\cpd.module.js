"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CPDModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const cpd_service_1 = require("./cpd.service");
const cpd_controller_1 = require("./cpd.controller");
const cpd_tracking_entity_1 = require("../../entities/cpd-tracking.entity");
const user_entity_1 = require("../../entities/user.entity");
const materials_entity_1 = require("../../entities/materials.entity");
let CPDModule = class CPDModule {
};
exports.CPDModule = CPDModule;
exports.CPDModule = CPDModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([cpd_tracking_entity_1.CPDActivity, cpd_tracking_entity_1.CPDCycle, user_entity_1.User, materials_entity_1.Material]),
        ],
        providers: [cpd_service_1.CPDService],
        controllers: [cpd_controller_1.CPDController],
        exports: [cpd_service_1.CPDService],
    })
], CPDModule);
//# sourceMappingURL=cpd.module.js.map