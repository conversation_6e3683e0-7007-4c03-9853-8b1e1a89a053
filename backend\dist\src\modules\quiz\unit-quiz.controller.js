"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnitQuizController = void 0;
const common_1 = require("@nestjs/common");
const unit_quiz_service_1 = require("./unit-quiz.service");
const jwt_auth_guard_1 = require("../auth/jwt-auth.guard");
let UnitQuizController = class UnitQuizController {
    constructor(unitQuizService) {
        this.unitQuizService = unitQuizService;
    }
    async getQuiz(unitId) {
        return this.unitQuizService.getQuizForUser(unitId);
    }
    async getEligibility(unitId, req) {
        return { eligible: await this.unitQuizService.validateAttempt(req.user.id, unitId) };
    }
    async submitQuiz(unitId, req, body) {
        return this.unitQuizService.scoreQuiz(req.user.id, unitId, body.answers);
    }
};
exports.UnitQuizController = UnitQuizController;
__decorate([
    (0, common_1.Get)(':unitId'),
    __param(0, (0, common_1.Param)('unitId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UnitQuizController.prototype, "getQuiz", null);
__decorate([
    (0, common_1.Get)(':unitId/eligibility'),
    __param(0, (0, common_1.Param)('unitId')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], UnitQuizController.prototype, "getEligibility", null);
__decorate([
    (0, common_1.Post)(':unitId/submit'),
    __param(0, (0, common_1.Param)('unitId')),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], UnitQuizController.prototype, "submitQuiz", null);
exports.UnitQuizController = UnitQuizController = __decorate([
    (0, common_1.Controller)('quiz/unit'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [unit_quiz_service_1.UnitQuizService])
], UnitQuizController);
//# sourceMappingURL=unit-quiz.controller.js.map