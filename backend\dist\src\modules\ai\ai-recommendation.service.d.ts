import { Repository } from 'typeorm';
import { User } from '../../entities/user.entity';
import { Material } from '../../entities/materials.entity';
import { LearningSuggestion } from '../../entities/learning-suggestion.entity';
import { AIService } from './ai.service';
interface Recommendation {
    materialId: string;
    score: number;
    reason: string;
}
export declare class AIRecommendationService {
    private readonly userRepository;
    private readonly materialRepository;
    private readonly learningSuggestionRepository;
    private readonly aiService;
    constructor(userRepository: Repository<User>, materialRepository: Repository<Material>, learningSuggestionRepository: Repository<LearningSuggestion>, aiService: AIService);
    getPersonalizedRecommendations(userId: string): Promise<Recommendation[]>;
    generateRecommendations(userId: string): Promise<Material[]>;
    private getUserLearningHistory;
    private getUserPreferences;
}
export {};
