import { RedisService } from '../redis/redis.service';
import { UserFeaturesService } from './user-features.service';
export interface ContentFeatures {
    contentId: string;
    contentType: string;
    difficulty: number;
    topics: string[];
    duration: number;
    engagement: number;
    completionRate: number;
    averageScore: number;
    prerequisites: string[];
    learningObjectives: string[];
    metadata: Record<string, any>;
}
export interface AIRecommendationInput {
    userId: string;
    userFeatures: number[];
    contentFeatures: ContentFeatures[];
    contextFeatures: number[];
    timestamp: Date;
}
export interface AIModelPrediction {
    contentId: string;
    score: number;
    confidence: number;
    reasoning: string[];
    category: string;
}
export interface FeatureImportance {
    featureName: string;
    importance: number;
    category: 'user' | 'content' | 'context';
}
export declare class AIFeaturesService {
    private readonly redisService;
    private readonly userFeaturesService;
    private readonly logger;
    private readonly CACHE_TTL;
    private readonly CONTENT_FEATURES_PREFIX;
    private readonly MODEL_CACHE_PREFIX;
    constructor(redisService: RedisService, userFeaturesService: UserFeaturesService);
    extractContentFeatures(contentId: string, contentData: any): Promise<ContentFeatures>;
    generateAIInput(userId: string, candidateContentIds: string[], context?: Record<string, any>): Promise<AIRecommendationInput>;
    createFeatureMatrix(userIds: string[], contentIds: string[], context?: Record<string, any>): Promise<{
        userFeatureMatrix: number[][];
        contentFeatureMatrix: number[][];
        contextFeatureMatrix: number[][];
        userIds: string[];
        contentIds: string[];
    }>;
    predictRecommendations(input: AIRecommendationInput): Promise<AIModelPrediction[]>;
    analyzeFeatureImportance(userId: string, contentIds: string[]): Promise<FeatureImportance[]>;
    private getContentData;
    private generateContentFeatures;
    private generateContextFeatures;
    private contentFeaturesToVector;
    private simulateMLPredictions;
    private getUserFeatureName;
    private getContentFeatureName;
    private getContextFeatureName;
}
