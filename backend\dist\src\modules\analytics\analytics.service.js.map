{"version": 3, "file": "analytics.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/analytics/analytics.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAuE;AACvE,yCAA4C;AAC5C,2CAA+C;AAC/C,+BAAsC;AACtC,6CAAmD;AACnD,qCAAqC;AACrC,qEAAyD;AAGlD,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAG3B,YACmB,WAAwB,EACxB,aAA4B,EAErC,iBAA4C;QAHnC,gBAAW,GAAX,WAAW,CAAa;QACxB,kBAAa,GAAb,aAAa,CAAe;QAErC,sBAAiB,GAAjB,iBAAiB,CAA2B;QAEpD,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,uBAAuB,CAAC,CAAC;QACpE,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QACD,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,MAAc;QACtC,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAA,qBAAc,EACnC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,YAAY,oCAAoC,MAAM,EAAE,CAAC,CACvF,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,mCAAmC,EACnC,mBAAU,CAAC,qBAAqB,CACjC,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,MAAc;QACrC,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAA,qBAAc,EACnC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,YAAY,kCAAkC,MAAM,EAAE,CAAC,CACrF,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,iCAAiC,EACjC,mBAAU,CAAC,qBAAqB,CACjC,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,MAAc;QACxC,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAA,qBAAc,EACnC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,YAAY,8BAA8B,MAAM,EAAE,CAAC,CACjF,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,qCAAqC,EACrC,mBAAU,CAAC,qBAAqB,CACjC,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,MAAc;QACpC,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAA,qBAAc,EACnC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,YAAY,6BAA6B,MAAM,EAAE,CAAC,CAChF,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,iCAAiC,EACjC,mBAAU,CAAC,qBAAqB,CACjC,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,MAAc,EAAE,SAAiB,EAAE,IAAS;QAC3D,MAAM,IAAA,qBAAc,EAClB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,YAAY,SAAS,EAAE;YACnD,MAAM;YACN,SAAS;YACT,IAAI;YACJ,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CACH,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,MAAc,EAAE,IAAY,EAAE,QAAa;QAC7D,MAAM,IAAA,qBAAc,EAClB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,YAAY,YAAY,EAAE;YACtD,MAAM;YACN,IAAI;YACJ,QAAQ;YACR,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CACH,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,MAAc,EAAE,KAAY,EAAE,OAAY;QACzD,MAAM,IAAA,qBAAc,EAClB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,YAAY,SAAS,EAAE;YACnD,MAAM;YACN,KAAK,EAAE;gBACL,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB;YACD,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CACH,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,OAAY;QACjD,MAAM,IAAA,qBAAc,EAClB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,YAAY,cAAc,EAAE;YACxD,MAAM;YACN,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CACH,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,MAAc,EAAE,MAAc,EAAE,KAAa;IAGvE,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,MAAc;IAK1C,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,MAAc,EAAE,MAAc;IAGzD,CAAC;CACF,CAAA;AAtIY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;IAOR,WAAA,IAAA,0BAAgB,EAAC,sCAAa,CAAC,CAAA;qCAFF,mBAAW;QACT,sBAAa;QAElB,oBAAU;GAP5B,gBAAgB,CAsI5B"}