{"version": 3, "file": "role-initialization.service.js", "sourceRoot": "", "sources": ["../../../../../src/modules/roles/services/role-initialization.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,qCAAqC;AACrC,+DAAqD;AACrD,2EAAiE;AAG1D,IAAM,yBAAyB,GAA/B,MAAM,yBAAyB;IAClC,YAEY,cAAgC,EAEhC,oBAA4C;QAF5C,mBAAc,GAAd,cAAc,CAAkB;QAEhC,yBAAoB,GAApB,oBAAoB,CAAwB;IACrD,CAAC;IAEJ,KAAK,CAAC,sBAAsB;QAExB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAG1D,MAAM,KAAK,GAAG;YACV;gBACI,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,mDAAmD;gBAChE,KAAK,EAAE,QAAQ;gBACf,eAAe,EAAE,CAAC;gBAClB,SAAS,EAAE,IAAI;gBACf,WAAW,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAChC,CAAC,cAAc,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,gBAAgB;oBACpE,iBAAiB,EAAE,cAAc,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CACvD;aACJ;YACD;gBACI,IAAI,EAAE,oBAAoB;gBAC1B,WAAW,EAAE,8DAA8D;gBAC3E,KAAK,EAAE,MAAM;gBACb,eAAe,EAAE,CAAC;gBAClB,SAAS,EAAE,IAAI;gBACf,WAAW,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAChC,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,iBAAiB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAC7F;aACJ;YACD;gBACI,IAAI,EAAE,kBAAkB;gBACxB,WAAW,EAAE,qDAAqD;gBAClE,KAAK,EAAE,OAAO;gBACd,eAAe,EAAE,CAAC;gBAClB,SAAS,EAAE,IAAI;gBACf,WAAW,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAChC,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,gBAAgB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CACjG;aACJ;YACD;gBACI,IAAI,EAAE,iBAAiB;gBACvB,WAAW,EAAE,6CAA6C;gBAC1D,KAAK,EAAE,QAAQ;gBACf,eAAe,EAAE,CAAC;gBAClB,SAAS,EAAE,IAAI;gBACf,WAAW,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAChC,CAAC,gBAAgB,EAAE,mBAAmB,EAAE,eAAe,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAC5E;aACJ;YACD;gBACI,IAAI,EAAE,qBAAqB;gBAC3B,WAAW,EAAE,uEAAuE;gBACpF,KAAK,EAAE,KAAK;gBACZ,eAAe,EAAE,CAAC;gBAClB,SAAS,EAAE,IAAI;gBACf,WAAW,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAChC,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,iBAAiB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAChH;aACJ;SACJ,CAAC;QAEF,KAAK,MAAM,QAAQ,IAAI,KAAK,EAAE,CAAC;YAC3B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBACnD,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE;aACjC,CAAC,CAAC;YAEH,IAAI,CAAC,YAAY,EAAE,CAAC;gBAChB,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAClD,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,CAAC;QACL,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,wBAAwB;QAClC,MAAM,kBAAkB,GAAG;YACvB;gBACI,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,gCAAgC;gBAC7C,QAAQ,EAAE,OAAO;aACpB;YACD;gBACI,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,gCAAgC;gBAC7C,QAAQ,EAAE,SAAS;aACtB;YACD;gBACI,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,2BAA2B;gBACxC,QAAQ,EAAE,SAAS;aACtB;YACD;gBACI,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,iCAAiC;gBAC9C,QAAQ,EAAE,WAAW;aACxB;YACD;gBACI,IAAI,EAAE,iBAAiB;gBACvB,WAAW,EAAE,wBAAwB;gBACrC,QAAQ,EAAE,UAAU;aACvB;YACD;gBACI,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,8BAA8B;gBAC3C,QAAQ,EAAE,OAAO;aACpB;YACD;gBACI,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,oBAAoB;gBACjC,QAAQ,EAAE,SAAS;aACtB;YACD;gBACI,IAAI,EAAE,iBAAiB;gBACvB,WAAW,EAAE,sCAAsC;gBACnD,QAAQ,EAAE,OAAO;aACpB;YACD;gBACI,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,yBAAyB;gBACtC,QAAQ,EAAE,SAAS;aACtB;YACD;gBACI,IAAI,EAAE,qBAAqB;gBAC3B,WAAW,EAAE,gCAAgC;gBAC7C,QAAQ,EAAE,QAAQ;aACrB;YACD;gBACI,IAAI,EAAE,mBAAmB;gBACzB,WAAW,EAAE,4BAA4B;gBACzC,QAAQ,EAAE,QAAQ;aACrB;YACD;gBACI,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,wBAAwB;gBACrC,QAAQ,EAAE,WAAW;aACxB;YACD;gBACI,IAAI,EAAE,iBAAiB;gBACvB,WAAW,EAAE,8BAA8B;gBAC3C,QAAQ,EAAE,SAAS;aACtB;SACJ,CAAC;QAEF,MAAM,WAAW,GAAiB,EAAE,CAAC;QAErC,KAAK,MAAM,QAAQ,IAAI,kBAAkB,EAAE,CAAC;YACxC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;gBACzD,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE;aACjC,CAAC,CAAC;YAEH,IAAI,CAAC,YAAY,EAAE,CAAC;gBAChB,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAC9D,WAAW,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;YACvE,CAAC;iBAAM,CAAC;gBACJ,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACnC,CAAC;QACL,CAAC;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;CACJ,CAAA;AArKY,8DAAyB;oCAAzB,yBAAyB;IADrC,IAAA,mBAAU,GAAE;IAGJ,WAAA,IAAA,0BAAgB,EAAC,kBAAI,CAAC,CAAA;IAEtB,WAAA,IAAA,0BAAgB,EAAC,8BAAU,CAAC,CAAA;qCADL,oBAAU;QAEJ,oBAAU;GALnC,yBAAyB,CAqKrC"}