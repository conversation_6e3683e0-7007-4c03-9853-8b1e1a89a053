{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/lib/offline/syncService.ts"], "sourcesContent": ["import { openDB, DBSchema, IDBPDatabase } from 'idb';\r\n\r\ninterface SyncOutboxDB extends DBSchema {\r\n  outbox: {\r\n    key: string;\r\n    value: {\r\n      id: string;\r\n      endpoint: string;\r\n      method: 'POST' | 'PUT' | 'DELETE';\r\n      payload: any;\r\n      timestamp: number;\r\n      retryCount: number;\r\n      lastAttempt: number | null;\r\n      status: 'pending' | 'failed' | 'processing';\r\n    };\r\n    indexes: {\r\n      'by-timestamp': number;\r\n      'by-status': string;\r\n    };\r\n  };\r\n  syncStatus: {\r\n    key: string;\r\n    value: {\r\n      lastSyncTimestamp: number;\r\n      isOnline: boolean;\r\n      pendingChanges: number;\r\n    };\r\n  };\r\n}\r\n\r\nclass SyncService {\r\n  private db: IDBPDatabase<SyncOutboxDB> | null = null;\r\n  private readonly DB_NAME = 'sync-outbox-db';\r\n  private readonly DB_VERSION = 1;\r\n  private syncInProgress = false;\r\n  private onlineStatus = typeof navigator !== 'undefined' ? navigator.onLine : true;\r\n\r\n  constructor() {\r\n    if (typeof window !== 'undefined') {\r\n      window.addEventListener('online', this.handleOnlineStatus);\r\n      window.addEventListener('offline', this.handleOnlineStatus);\r\n    }\r\n  }\r\n\r\n  private handleOnlineStatus = () => {\r\n    this.onlineStatus = navigator.onLine;\r\n    if (this.onlineStatus) {\r\n      this.processOutbox();\r\n    }\r\n  };\r\n\r\n  async init() {\r\n    if (!this.db) {\r\n      this.db = await openDB<SyncOutboxDB>(this.DB_NAME, this.DB_VERSION, {\r\n        upgrade(db) {\r\n          const outboxStore = db.createObjectStore('outbox', { keyPath: 'id' });\r\n          outboxStore.createIndex('by-timestamp', 'timestamp');\r\n          outboxStore.createIndex('by-status', 'status');\r\n\r\n          db.createObjectStore('syncStatus', { keyPath: 'id' });\r\n        },\r\n      });\r\n\r\n      // Initialize sync status\r\n      const tx = this.db.transaction('syncStatus', 'readwrite');\r\n      await tx.store.put({\r\n        id: 'current',\r\n        lastSyncTimestamp: Date.now(),\r\n        isOnline: this.onlineStatus,\r\n        pendingChanges: 0,\r\n      });\r\n      await tx.done;\r\n    }\r\n    return this.db;\r\n  }\r\n\r\n  async addToOutbox(endpoint: string, method: 'POST' | 'PUT' | 'DELETE', payload: any) {\r\n    const db = await this.init();\r\n    const tx = db.transaction('outbox', 'readwrite');\r\n    const item = {\r\n      id: crypto.randomUUID(),\r\n      endpoint,\r\n      method,\r\n      payload,\r\n      timestamp: Date.now(),\r\n      retryCount: 0,\r\n      lastAttempt: null,\r\n      status: 'pending' as const,\r\n    };\r\n    await tx.store.add(item);\r\n    await this.updateSyncStatus();\r\n    await tx.done;\r\n\r\n    if (this.onlineStatus) {\r\n      this.processOutbox();\r\n    }\r\n  }\r\n\r\n  private async updateSyncStatus() {\r\n    const db = await this.init();\r\n    const tx = db.transaction(['outbox', 'syncStatus'], 'readwrite');\r\n    const pendingCount = await tx.store.index('by-status').count('pending');\r\n    \r\n    await tx.objectStore('syncStatus').put({\r\n      id: 'current',\r\n      lastSyncTimestamp: Date.now(),\r\n      isOnline: this.onlineStatus,\r\n      pendingChanges: pendingCount,\r\n    });\r\n    \r\n    await tx.done;\r\n  }\r\n\r\n  async getSyncStatus() {\r\n    const db = await this.init();\r\n    const tx = db.transaction('syncStatus', 'readonly');\r\n    return tx.store.get('current');\r\n  }\r\n\r\n  private async processOutbox() {\r\n    if (this.syncInProgress || !this.onlineStatus) return;\r\n\r\n    try {\r\n      this.syncInProgress = true;\r\n      const db = await this.init();\r\n      const tx = db.transaction('outbox', 'readwrite');\r\n      const pendingItems = await tx.store.index('by-status').getAll('pending');\r\n\r\n      for (const item of pendingItems) {\r\n        try {\r\n          item.status = 'processing';\r\n          await tx.store.put(item);\r\n\r\n          const response = await fetch(item.endpoint, {\r\n            method: item.method,\r\n            headers: {\r\n              'Content-Type': 'application/json',\r\n            },\r\n            body: JSON.stringify(item.payload),\r\n          });\r\n\r\n          if (response.ok) {\r\n            await tx.store.delete(item.id);\r\n          } else {\r\n            item.status = 'failed';\r\n            item.retryCount++;\r\n            item.lastAttempt = Date.now();\r\n            await tx.store.put(item);\r\n          }\r\n        } catch (error) {\r\n          item.status = 'failed';\r\n          item.retryCount++;\r\n          item.lastAttempt = Date.now();\r\n          await tx.store.put(item);\r\n        }\r\n      }\r\n\r\n      await this.updateSyncStatus();\r\n    } finally {\r\n      this.syncInProgress = false;\r\n    }\r\n  }\r\n}\r\n\r\nexport const syncService = new SyncService(); "], "names": [], "mappings": ";;;;AAAA;;;AA8BA,MAAM;IAqBJ,MAAM,OAAO;QACX,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;YACZ,IAAI,CAAC,EAAE,GAAG,MAAM,CAAA,GAAA,wLAAA,CAAA,SAAM,AAAD,EAAgB,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;gBAClE,SAAQ,EAAE;oBACR,MAAM,cAAc,GAAG,iBAAiB,CAAC,UAAU;wBAAE,SAAS;oBAAK;oBACnE,YAAY,WAAW,CAAC,gBAAgB;oBACxC,YAAY,WAAW,CAAC,aAAa;oBAErC,GAAG,iBAAiB,CAAC,cAAc;wBAAE,SAAS;oBAAK;gBACrD;YACF;YAEA,yBAAyB;YACzB,MAAM,KAAK,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,cAAc;YAC7C,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC;gBACjB,IAAI;gBACJ,mBAAmB,KAAK,GAAG;gBAC3B,UAAU,IAAI,CAAC,YAAY;gBAC3B,gBAAgB;YAClB;YACA,MAAM,GAAG,IAAI;QACf;QACA,OAAO,IAAI,CAAC,EAAE;IAChB;IAEA,MAAM,YAAY,QAAgB,EAAE,MAAiC,EAAE,OAAY,EAAE;QACnF,MAAM,KAAK,MAAM,IAAI,CAAC,IAAI;QAC1B,MAAM,KAAK,GAAG,WAAW,CAAC,UAAU;QACpC,MAAM,OAAO;YACX,IAAI,OAAO,UAAU;YACrB;YACA;YACA;YACA,WAAW,KAAK,GAAG;YACnB,YAAY;YACZ,aAAa;YACb,QAAQ;QACV;QACA,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC;QACnB,MAAM,IAAI,CAAC,gBAAgB;QAC3B,MAAM,GAAG,IAAI;QAEb,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,aAAa;QACpB;IACF;IAEA,MAAc,mBAAmB;QAC/B,MAAM,KAAK,MAAM,IAAI,CAAC,IAAI;QAC1B,MAAM,KAAK,GAAG,WAAW,CAAC;YAAC;YAAU;SAAa,EAAE;QACpD,MAAM,eAAe,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,aAAa,KAAK,CAAC;QAE7D,MAAM,GAAG,WAAW,CAAC,cAAc,GAAG,CAAC;YACrC,IAAI;YACJ,mBAAmB,KAAK,GAAG;YAC3B,UAAU,IAAI,CAAC,YAAY;YAC3B,gBAAgB;QAClB;QAEA,MAAM,GAAG,IAAI;IACf;IAEA,MAAM,gBAAgB;QACpB,MAAM,KAAK,MAAM,IAAI,CAAC,IAAI;QAC1B,MAAM,KAAK,GAAG,WAAW,CAAC,cAAc;QACxC,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC;IACtB;IAEA,MAAc,gBAAgB;QAC5B,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;QAE/C,IAAI;YACF,IAAI,CAAC,cAAc,GAAG;YACtB,MAAM,KAAK,MAAM,IAAI,CAAC,IAAI;YAC1B,MAAM,KAAK,GAAG,WAAW,CAAC,UAAU;YACpC,MAAM,eAAe,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,aAAa,MAAM,CAAC;YAE9D,KAAK,MAAM,QAAQ,aAAc;gBAC/B,IAAI;oBACF,KAAK,MAAM,GAAG;oBACd,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC;oBAEnB,MAAM,WAAW,MAAM,MAAM,KAAK,QAAQ,EAAE;wBAC1C,QAAQ,KAAK,MAAM;wBACnB,SAAS;4BACP,gBAAgB;wBAClB;wBACA,MAAM,KAAK,SAAS,CAAC,KAAK,OAAO;oBACnC;oBAEA,IAAI,SAAS,EAAE,EAAE;wBACf,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE;oBAC/B,OAAO;wBACL,KAAK,MAAM,GAAG;wBACd,KAAK,UAAU;wBACf,KAAK,WAAW,GAAG,KAAK,GAAG;wBAC3B,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC;oBACrB;gBACF,EAAE,OAAO,OAAO;oBACd,KAAK,MAAM,GAAG;oBACd,KAAK,UAAU;oBACf,KAAK,WAAW,GAAG,KAAK,GAAG;oBAC3B,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC;gBACrB;YACF;YAEA,MAAM,IAAI,CAAC,gBAAgB;QAC7B,SAAU;YACR,IAAI,CAAC,cAAc,GAAG;QACxB;IACF;IA5HA,aAAc;QANd,6OAAQ,MAAwC;QAChD,6OAAiB,WAAU;QAC3B,6OAAiB,cAAa;QAC9B,6OAAQ,kBAAiB;QACzB,6OAAQ,gBAAe,OAAO,cAAc,cAAc,UAAU,MAAM,GAAG;QAS7E,6OAAQ,sBAAqB;YAC3B,IAAI,CAAC,YAAY,GAAG,UAAU,MAAM;YACpC,IAAI,IAAI,CAAC,YAAY,EAAE;gBACrB,IAAI,CAAC,aAAa;YACpB;QACF;QAXE,wCAAmC;YACjC,OAAO,gBAAgB,CAAC,UAAU,IAAI,CAAC,kBAAkB;YACzD,OAAO,gBAAgB,CAAC,WAAW,IAAI,CAAC,kBAAkB;QAC5D;IACF;AAwHF;AAEO,MAAM,cAAc,IAAI", "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/components/SyncStatusBanner.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport { syncService } from '@/lib/offline/syncService';\r\nimport { AlertCircle, CheckCircle2, Clock } from 'lucide-react';\r\n\r\ninterface SyncStatus {\r\n  lastSyncTimestamp: number;\r\n  isOnline: boolean;\r\n  pendingChanges: number;\r\n}\r\n\r\nexport function SyncStatusBanner() {\r\n  const [status, setStatus] = useState<SyncStatus | null>(null);\r\n\r\n  useEffect(() => {\r\n    const updateStatus = async () => {\r\n      const currentStatus: SyncStatus = await syncService.getSyncStatus();\r\n      setStatus(currentStatus);\r\n    };\r\n\r\n    updateStatus();\r\n    const interval = setInterval(updateStatus, 5000);\r\n    return () => clearInterval(interval);\r\n  }, []);\r\n\r\n  if (!status) return null;\r\n\r\n  const getStatusColor = () => {\r\n    if (!status.isOnline) return 'bg-yellow-100 text-yellow-800';\r\n    if (status.pendingChanges > 0) return 'bg-blue-100 text-blue-800';\r\n    return 'bg-green-100 text-green-800';\r\n  };\r\n\r\n  const getStatusIcon = () => {\r\n    if (!status.isOnline) return <AlertCircle className=\"h-5 w-5\" />;\r\n    if (status.pendingChanges > 0) return <Clock className=\"h-5 w-5\" />;\r\n    return <CheckCircle2 className=\"h-5 w-5\" />;\r\n  };\r\n\r\n  const getStatusMessage = () => {\r\n    if (!status.isOnline) return 'You are offline. Changes will sync when you reconnect.';\r\n    if (status.pendingChanges > 0) return `${status.pendingChanges} changes pending sync`;\r\n    return 'All changes synced';\r\n  };\r\n\r\n  return (\r\n    <div className={`fixed bottom-0 left-0 right-0 p-2 ${getStatusColor()} transition-colors duration-300`}>\r\n      <div className=\"container mx-auto flex items-center justify-center gap-2\">\r\n        {getStatusIcon()}\r\n        <span className=\"text-sm font-medium\">{getStatusMessage()}</span>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;;;AAJA;;;;AAYO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAqB;IAExD,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;2DAAe;oBACnB,MAAM,gBAA4B,MAAM,uIAAA,CAAA,cAAW,CAAC,aAAa;oBACjE,UAAU;gBACZ;;YAEA;YACA,MAAM,WAAW,YAAY,cAAc;YAC3C;8CAAO,IAAM,cAAc;;QAC7B;qCAAG,EAAE;IAEL,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,iBAAiB;QACrB,IAAI,CAAC,OAAO,QAAQ,EAAE,OAAO;QAC7B,IAAI,OAAO,cAAc,GAAG,GAAG,OAAO;QACtC,OAAO;IACT;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,OAAO,QAAQ,EAAE,qBAAO,4TAAC,2SAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QACpD,IAAI,OAAO,cAAc,GAAG,GAAG,qBAAO,4TAAC,2RAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QACvD,qBAAO,4TAAC,iTAAA,CAAA,eAAY;YAAC,WAAU;;;;;;IACjC;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,OAAO,QAAQ,EAAE,OAAO;QAC7B,IAAI,OAAO,cAAc,GAAG,GAAG,OAAO,AAAC,GAAwB,OAAtB,OAAO,cAAc,EAAC;QAC/D,OAAO;IACT;IAEA,qBACE,4TAAC;QAAI,WAAW,AAAC,qCAAqD,OAAjB,kBAAiB;kBACpE,cAAA,4TAAC;YAAI,WAAU;;gBACZ;8BACD,4TAAC;oBAAK,WAAU;8BAAuB;;;;;;;;;;;;;;;;;AAI/C;GA1CgB;KAAA", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/app/ServiceWorkerRegister.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect } from \"react\";\r\n\r\nexport function ServiceWorkerRegister() {\r\n  useEffect(() => {\r\n    if (\"serviceWorker\" in navigator) {\r\n      navigator.serviceWorker.register(\"/sw.js\").then(\r\n        () => {\r\n          console.log(\"ServiceWorker registered\");\r\n        },\r\n        (err) => {\r\n          console.error(\"ServiceWorker registration failed:\", err);\r\n        }\r\n      );\r\n    }\r\n  }, []);\r\n\r\n  return null;\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAIO,SAAS;;IACd,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;2CAAE;YACR,IAAI,mBAAmB,WAAW;gBAChC,UAAU,aAAa,CAAC,QAAQ,CAAC,UAAU,IAAI;uDAC7C;wBACE,QAAQ,GAAG,CAAC;oBACd;;uDACA,CAAC;wBACC,QAAQ,KAAK,CAAC,sCAAsC;oBACtD;;YAEJ;QACF;0CAAG,EAAE;IAEL,OAAO;AACT;GAfgB;KAAA", "debugId": null}}, {"offset": {"line": 292, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/app/ClientLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport dynamic from 'next/dynamic';\r\nimport { ReactNode } from 'react';\r\nimport { Toaster } from 'react-hot-toast';\r\nimport { SyncStatusBanner } from '@/components/SyncStatusBanner';\r\nimport { ServiceWorkerRegister } from './ServiceWorkerRegister';\r\n\r\n// Dynamically import MedTrackLayout with SSR disabled\r\nconst MedTrackLayout = dynamic(() => import('./MedTrackLayout'), { ssr: false });\r\n\r\ninterface ClientLayoutProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport function ClientLayout({ children }: ClientLayoutProps) {\r\n  return (\r\n    <MedTrackLayout>\r\n      <ServiceWorkerRegister />\r\n      <Toaster position=\"top-right\" />\r\n      <SyncStatusBanner />\r\n      {children}\r\n    </MedTrackLayout>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;;AANA;;;;;;AAQA,sDAAsD;AACtD,MAAM,iBAAiB,CAAA,GAAA,iSAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IAAoC,KAAK;;KAAlE;AAMC,SAAS,aAAa,KAA+B;QAA/B,EAAE,QAAQ,EAAqB,GAA/B;IAC3B,qBACE,4TAAC;;0BACC,4TAAC,uIAAA,CAAA,wBAAqB;;;;;0BACtB,4TAAC,mQAAA,CAAA,UAAO;gBAAC,UAAS;;;;;;0BAClB,4TAAC,yIAAA,CAAA,mBAAgB;;;;;YAChB;;;;;;;AAGP;MATgB", "debugId": null}}, {"offset": {"line": 359, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/services/rateLimiter.ts"], "sourcesContent": ["interface RateLimitConfig {\r\n  maxRequests: number;\r\n  timeWindow: number; // in milliseconds\r\n}\r\n\r\ninterface RateLimitState {\r\n  requests: number;\r\n  resetTime: number;\r\n}\r\n\r\nclass RateLimiter {\r\n  private static instance: RateLimiter;\r\n  private limits: Map<string, RateLimitConfig>;\r\n  private states: Map<string, RateLimitState>;\r\n\r\n  private constructor() {\r\n    this.limits = new Map();\r\n    this.states = new Map();\r\n  }\r\n\r\n  static getInstance(): RateLimiter {\r\n    if (!RateLimiter.instance) {\r\n      RateLimiter.instance = new RateLimiter();\r\n    }\r\n    return RateLimiter.instance;\r\n  }\r\n\r\n  setLimit(endpoint: string, config: RateLimitConfig): void {\r\n    this.limits.set(endpoint, config);\r\n  }\r\n\r\n  async checkLimit(endpoint: string): Promise<boolean> {\r\n    const config = this.limits.get(endpoint);\r\n    if (!config) return true; // No limit set for this endpoint\r\n\r\n    const now = Date.now();\r\n    const state = this.states.get(endpoint) || { requests: 0, resetTime: now + config.timeWindow };\r\n\r\n    // Reset if time window has passed\r\n    if (now > state.resetTime) {\r\n      state.requests = 0;\r\n      state.resetTime = now + config.timeWindow;\r\n    }\r\n\r\n    // Check if limit is exceeded\r\n    if (state.requests >= config.maxRequests) {\r\n      return false;\r\n    }\r\n\r\n    // Increment request count\r\n    state.requests++;\r\n    this.states.set(endpoint, state);\r\n    return true;\r\n  }\r\n\r\n  getRemainingRequests(endpoint: string): number {\r\n    const config = this.limits.get(endpoint);\r\n    if (!config) return Infinity;\r\n\r\n    const state = this.states.get(endpoint);\r\n    if (!state) return config.maxRequests;\r\n\r\n    return Math.max(0, config.maxRequests - state.requests);\r\n  }\r\n\r\n  getResetTime(endpoint: string): number {\r\n    const state = this.states.get(endpoint);\r\n    return state?.resetTime || Date.now();\r\n  }\r\n\r\n  reset(endpoint: string): void {\r\n    this.states.delete(endpoint);\r\n  }\r\n}\r\n\r\nexport const rateLimiter = RateLimiter.getInstance();\r\n\r\n// Default rate limits\r\nrateLimiter.setLimit('auth/login', { maxRequests: 5, timeWindow: 60000 }); // 5 requests per minute\r\nrateLimiter.setLimit('auth/register', { maxRequests: 3, timeWindow: 3600000 }); // 3 requests per hour\r\nrateLimiter.setLimit('auth/forgot-password', { maxRequests: 3, timeWindow: 3600000 }); // 3 requests per hour\r\nrateLimiter.setLimit('api/*', { maxRequests: 100, timeWindow: 60000 }); // 100 requests per minute for general API "], "names": [], "mappings": ";;;;;AAUA,MAAM;IAUJ,OAAO,cAA2B;QAChC,IAAI,CAAC,YAAY,QAAQ,EAAE;YACzB,YAAY,QAAQ,GAAG,IAAI;QAC7B;QACA,OAAO,YAAY,QAAQ;IAC7B;IAEA,SAAS,QAAgB,EAAE,MAAuB,EAAQ;QACxD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU;IAC5B;IAEA,MAAM,WAAW,QAAgB,EAAoB;QACnD,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;QAC/B,IAAI,CAAC,QAAQ,OAAO,MAAM,iCAAiC;QAE3D,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa;YAAE,UAAU;YAAG,WAAW,MAAM,OAAO,UAAU;QAAC;QAE7F,kCAAkC;QAClC,IAAI,MAAM,MAAM,SAAS,EAAE;YACzB,MAAM,QAAQ,GAAG;YACjB,MAAM,SAAS,GAAG,MAAM,OAAO,UAAU;QAC3C;QAEA,6BAA6B;QAC7B,IAAI,MAAM,QAAQ,IAAI,OAAO,WAAW,EAAE;YACxC,OAAO;QACT;QAEA,0BAA0B;QAC1B,MAAM,QAAQ;QACd,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU;QAC1B,OAAO;IACT;IAEA,qBAAqB,QAAgB,EAAU;QAC7C,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;QAC/B,IAAI,CAAC,QAAQ,OAAO;QAEpB,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;QAC9B,IAAI,CAAC,OAAO,OAAO,OAAO,WAAW;QAErC,OAAO,KAAK,GAAG,CAAC,GAAG,OAAO,WAAW,GAAG,MAAM,QAAQ;IACxD;IAEA,aAAa,QAAgB,EAAU;QACrC,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;QAC9B,OAAO,CAAA,kBAAA,4BAAA,MAAO,SAAS,KAAI,KAAK,GAAG;IACrC;IAEA,MAAM,QAAgB,EAAQ;QAC5B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IACrB;IAzDA,aAAsB;QAHtB,6OAAQ,UAAR,KAAA;QACA,6OAAQ,UAAR,KAAA;QAGE,IAAI,CAAC,MAAM,GAAG,IAAI;QAClB,IAAI,CAAC,MAAM,GAAG,IAAI;IACpB;AAuDF;AA9DE,uOADI,aACW,YAAf,KAAA;AAgEK,MAAM,cAAc,YAAY,WAAW;AAElD,sBAAsB;AACtB,YAAY,QAAQ,CAAC,cAAc;IAAE,aAAa;IAAG,YAAY;AAAM,IAAI,wBAAwB;AACnG,YAAY,QAAQ,CAAC,iBAAiB;IAAE,aAAa;IAAG,YAAY;AAAQ,IAAI,sBAAsB;AACtG,YAAY,QAAQ,CAAC,wBAAwB;IAAE,aAAa;IAAG,YAAY;AAAQ,IAAI,sBAAsB;AAC7G,YAAY,QAAQ,CAAC,SAAS;IAAE,aAAa;IAAK,YAAY;AAAM,IAAI,2CAA2C", "debugId": null}}, {"offset": {"line": 445, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\nimport { ApiError, ApiResponse, RequestOptions } from '@/types/api';\nimport { rateLimiter } from './rateLimiter';\nimport Cookies from 'js-cookie';\n\nclass ApiService {\n  private static instance: ApiService;\n  private api: AxiosInstance;\n\n  private constructor() {\n    this.api = axios.create({\n      baseURL: process.env.NEXT_PUBLIC_API_URL,\n      timeout: 30000,\n      withCredentials: true,\n    });\n\n    this.setupInterceptors();\n  }\n\n  static getInstance(): ApiService {\n    if (!ApiService.instance) {\n      ApiService.instance = new ApiService();\n    }\n    return ApiService.instance;\n  }\n\n  private setupInterceptors(): void {\n    // Request interceptor\n    this.api.interceptors.request.use(\n      async (config) => {\n        try {\n          // Check rate limit\n          const endpoint = config.url?.replace(/^\\//, '') || '';\n          const isAllowed = await rateLimiter.checkLimit(endpoint);\n          \n          if (!isAllowed) {\n            const resetTime = rateLimiter.getResetTime(endpoint);\n            const remainingTime = Math.ceil((resetTime - Date.now()) / 1000);\n            throw new Error(`Rate limit exceeded. Please try again in ${remainingTime} seconds.`);\n          }\n\n          const token = Cookies.get('access_token');\n          if (token) {\n            config.headers.Authorization = `Bearer ${token}`;\n          }\n          return config;\n        } catch (error) {\n          return Promise.reject(error);\n        }\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // Response interceptor\n    this.api.interceptors.response.use(\n      (response) => response,\n      async (error) => {\n        const originalRequest = error.config;\n\n        // Handle token refresh\n        if (error.response?.status === 401 && !originalRequest._retry) {\n          originalRequest._retry = true;\n\n          try {\n            await authService.getAccessToken();\n            return this.api(originalRequest);\n          } catch (refreshError) {\n            return Promise.reject(refreshError);\n          }\n        }\n\n        // Transform error response\n        const apiError: ApiError = {\n          code: error.response?.data?.code || 'UNKNOWN_ERROR',\n          message: error.response?.data?.message || error.message || 'An unexpected error occurred',\n          details: error.response?.data?.details,\n          status: error.response?.status || 500,\n        };\n\n        return Promise.reject(apiError);\n      }\n    );\n  }\n\n  async get<T>(url: string, options?: RequestOptions): Promise<ApiResponse<T>> {\n    const config: AxiosRequestConfig = {\n      headers: options?.headers,\n      params: options?.params,\n      timeout: options?.timeout,\n      withCredentials: options?.withCredentials,\n    };\n\n    const response: AxiosResponse<ApiResponse<T>> = await this.api.get(url, config);\n    return response.data;\n  }\n\n  async post<T>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {\n    const config: AxiosRequestConfig = {\n      headers: options?.headers,\n      params: options?.params,\n      timeout: options?.timeout,\n      withCredentials: options?.withCredentials,\n    };\n\n    const response: AxiosResponse<ApiResponse<T>> = await this.api.post(url, data, config);\n    return response.data;\n  }\n\n  async put<T>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {\n    const config: AxiosRequestConfig = {\n      headers: options?.headers,\n      params: options?.params,\n      timeout: options?.timeout,\n      withCredentials: options?.withCredentials,\n    };\n\n    const response: AxiosResponse<ApiResponse<T>> = await this.api.put(url, data, config);\n    return response.data;\n  }\n\n  async patch<T>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {\n    const config: AxiosRequestConfig = {\n      headers: options?.headers,\n      params: options?.params,\n      timeout: options?.timeout,\n      withCredentials: options?.withCredentials,\n    };\n\n    const response: AxiosResponse<ApiResponse<T>> = await this.api.patch(url, data, config);\n    return response.data;\n  }\n\n  async delete<T>(url: string, options?: RequestOptions): Promise<ApiResponse<T>> {\n    const config: AxiosRequestConfig = {\n      headers: options?.headers,\n      params: options?.params,\n      timeout: options?.timeout,\n      withCredentials: options?.withCredentials,\n    };\n\n    const response: AxiosResponse<ApiResponse<T>> = await this.api.delete(url, config);\n    return response.data;\n  }\n}\n\nconst apiService = ApiService.getInstance();\nexport { apiService };\nexport default apiService;\n"], "names": [], "mappings": ";;;;AAWe;;AAXf;AAEA;AACA;;;;;AAEA,MAAM;IAcJ,OAAO,cAA0B;QAC/B,IAAI,CAAC,WAAW,QAAQ,EAAE;YACxB,WAAW,QAAQ,GAAG,IAAI;QAC5B;QACA,OAAO,WAAW,QAAQ;IAC5B;IAEQ,oBAA0B;QAChC,sBAAsB;QACtB,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAC/B,OAAO;YACL,IAAI;oBAEe;gBADjB,mBAAmB;gBACnB,MAAM,WAAW,EAAA,cAAA,OAAO,GAAG,cAAV,kCAAA,YAAY,OAAO,CAAC,OAAO,QAAO;gBACnD,MAAM,YAAY,MAAM,iIAAA,CAAA,cAAW,CAAC,UAAU,CAAC;gBAE/C,IAAI,CAAC,WAAW;oBACd,MAAM,YAAY,iIAAA,CAAA,cAAW,CAAC,YAAY,CAAC;oBAC3C,MAAM,gBAAgB,KAAK,IAAI,CAAC,CAAC,YAAY,KAAK,GAAG,EAAE,IAAI;oBAC3D,MAAM,IAAI,MAAM,AAAC,4CAAyD,OAAd,eAAc;gBAC5E;gBAEA,MAAM,QAAQ,iNAAA,CAAA,UAAO,CAAC,GAAG,CAAC;gBAC1B,IAAI,OAAO;oBACT,OAAO,OAAO,CAAC,aAAa,GAAG,AAAC,UAAe,OAAN;gBAC3C;gBACA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,OAAO,QAAQ,MAAM,CAAC;YACxB;QACF,GACA,CAAC;YACC,OAAO,QAAQ,MAAM,CAAC;QACxB;QAGF,uBAAuB;QACvB,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAChC,CAAC,WAAa,UACd,OAAO;gBAID,iBAaI,sBAAA,kBACG,uBAAA,kBACA,uBAAA,kBACD;YAnBV,MAAM,kBAAkB,MAAM,MAAM;YAEpC,uBAAuB;YACvB,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,OAAO,CAAC,gBAAgB,MAAM,EAAE;gBAC7D,gBAAgB,MAAM,GAAG;gBAEzB,IAAI;oBACF,MAAM,YAAY,cAAc;oBAChC,OAAO,IAAI,CAAC,GAAG,CAAC;gBAClB,EAAE,OAAO,cAAc;oBACrB,OAAO,QAAQ,MAAM,CAAC;gBACxB;YACF;YAEA,2BAA2B;YAC3B,MAAM,WAAqB;gBACzB,MAAM,EAAA,mBAAA,MAAM,QAAQ,cAAd,wCAAA,uBAAA,iBAAgB,IAAI,cAApB,2CAAA,qBAAsB,IAAI,KAAI;gBACpC,SAAS,EAAA,mBAAA,MAAM,QAAQ,cAAd,wCAAA,wBAAA,iBAAgB,IAAI,cAApB,4CAAA,sBAAsB,OAAO,KAAI,MAAM,OAAO,IAAI;gBAC3D,OAAO,GAAE,mBAAA,MAAM,QAAQ,cAAd,wCAAA,wBAAA,iBAAgB,IAAI,cAApB,4CAAA,sBAAsB,OAAO;gBACtC,QAAQ,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,MAAM,KAAI;YACpC;YAEA,OAAO,QAAQ,MAAM,CAAC;QACxB;IAEJ;IAEA,MAAM,IAAO,GAAW,EAAE,OAAwB,EAA2B;QAC3E,MAAM,SAA6B;YACjC,OAAO,EAAE,oBAAA,8BAAA,QAAS,OAAO;YACzB,MAAM,EAAE,oBAAA,8BAAA,QAAS,MAAM;YACvB,OAAO,EAAE,oBAAA,8BAAA,QAAS,OAAO;YACzB,eAAe,EAAE,oBAAA,8BAAA,QAAS,eAAe;QAC3C;QAEA,MAAM,WAA0C,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK;QACxE,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,KAAQ,GAAW,EAAE,IAAU,EAAE,OAAwB,EAA2B;QACxF,MAAM,SAA6B;YACjC,OAAO,EAAE,oBAAA,8BAAA,QAAS,OAAO;YACzB,MAAM,EAAE,oBAAA,8BAAA,QAAS,MAAM;YACvB,OAAO,EAAE,oBAAA,8BAAA,QAAS,OAAO;YACzB,eAAe,EAAE,oBAAA,8BAAA,QAAS,eAAe;QAC3C;QAEA,MAAM,WAA0C,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,MAAM;QAC/E,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,IAAO,GAAW,EAAE,IAAU,EAAE,OAAwB,EAA2B;QACvF,MAAM,SAA6B;YACjC,OAAO,EAAE,oBAAA,8BAAA,QAAS,OAAO;YACzB,MAAM,EAAE,oBAAA,8BAAA,QAAS,MAAM;YACvB,OAAO,EAAE,oBAAA,8BAAA,QAAS,OAAO;YACzB,eAAe,EAAE,oBAAA,8BAAA,QAAS,eAAe;QAC3C;QAEA,MAAM,WAA0C,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,MAAM;QAC9E,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,MAAS,GAAW,EAAE,IAAU,EAAE,OAAwB,EAA2B;QACzF,MAAM,SAA6B;YACjC,OAAO,EAAE,oBAAA,8BAAA,QAAS,OAAO;YACzB,MAAM,EAAE,oBAAA,8BAAA,QAAS,MAAM;YACvB,OAAO,EAAE,oBAAA,8BAAA,QAAS,OAAO;YACzB,eAAe,EAAE,oBAAA,8BAAA,QAAS,eAAe;QAC3C;QAEA,MAAM,WAA0C,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,MAAM;QAChF,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,OAAU,GAAW,EAAE,OAAwB,EAA2B;QAC9E,MAAM,SAA6B;YACjC,OAAO,EAAE,oBAAA,8BAAA,QAAS,OAAO;YACzB,MAAM,EAAE,oBAAA,8BAAA,QAAS,MAAM;YACvB,OAAO,EAAE,oBAAA,8BAAA,QAAS,OAAO;YACzB,eAAe,EAAE,oBAAA,8BAAA,QAAS,eAAe;QAC3C;QAEA,MAAM,WAA0C,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK;QAC3E,OAAO,SAAS,IAAI;IACtB;IAvIA,aAAsB;QAFtB,6OAAQ,OAAR,KAAA;QAGE,IAAI,CAAC,GAAG,GAAG,2LAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YACtB,OAAO;YACP,SAAS;YACT,iBAAiB;QACnB;QAEA,IAAI,CAAC,iBAAiB;IACxB;AAgIF;AA3IE,uOADI,YACW,YAAf,KAAA;AA6IF,MAAM,aAAa,WAAW,WAAW;;uCAE1B", "debugId": null}}, {"offset": {"line": 586, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { apiService } from '@/services/api';\r\nimport { User } from '@/types';\r\n\r\ninterface AuthContextType {\r\n  user: User | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n  login: (email: string, password: string) => Promise<void>;\r\n  logout: () => Promise<void>;\r\n  register: (userData: Partial<User>) => Promise<void>;\r\n  updateProfile: (userData: Partial<User>) => Promise<void>;\r\n  isAuthenticated: boolean;\r\n}\r\n\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\r\n\r\nexport const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    checkAuth();\r\n  }, []);\r\n\r\n  const checkAuth = async () => {\r\n    try {\r\n      const token = localStorage.getItem('token');\r\n      if (token) {\r\n        const response = await apiService.get<User>('/auth/me');\r\n        setUser(response.data);\r\n      }\r\n    } catch (error) {\r\n      localStorage.removeItem('token');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const login = async (email: string, password: string) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      const response = await apiService.post<{ token: string; user: User }>('/auth/login', {\r\n        email,\r\n        password,\r\n      });\r\n      localStorage.setItem('token', response.data.token);\r\n      setUser(response.data.user);\r\n      router.push('/dashboard');\r\n    } catch (error: any) {\r\n      setError(error.response?.data?.message || 'Login failed');\r\n      throw error;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const logout = async () => {\r\n    try {\r\n      setLoading(true);\r\n      await apiService.post('/auth/logout');\r\n      localStorage.removeItem('token');\r\n      setUser(null);\r\n      router.push('/auth/login');\r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const register = async (userData: Partial<User>) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      const response = await apiService.post<{ token: string; user: User }>('/auth/register', userData);\r\n      localStorage.setItem('token', response.data.token);\r\n      setUser(response.data.user);\r\n      router.push('/dashboard');\r\n    } catch (error: any) {\r\n      setError(error.response?.data?.message || 'Registration failed');\r\n      throw error;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const updateProfile = async (userData: Partial<User>) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      const response = await apiService.put<User>('/auth/profile', userData);\r\n      setUser(response.data);\r\n    } catch (error: any) {\r\n      setError(error.response?.data?.message || 'Profile update failed');\r\n      throw error;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const value = {\r\n    user,\r\n    loading,\r\n    error,\r\n    login,\r\n    logout,\r\n    register,\r\n    updateProfile,\r\n    isAuthenticated: !!user,\r\n  };\r\n\r\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\r\n};\r\n\r\nexport const useAuth = () => {\r\n  const context = useContext(AuthContext);\r\n  if (context === undefined) {\r\n    throw new Error('useAuth must be used within an AuthProvider');\r\n  }\r\n  return context;\r\n}; \r\ninterface User {\r\n  id: string;\r\n  name: string;\r\n  email: string;\r\n  avatar?: string;\r\n  role: string;\r\n  isAuthenticated: boolean;\r\n}\r\n\r\ninterface AuthContextType {\r\n  user: User | null;\r\n  isAuthenticated: boolean;\r\n  logout: () => void;\r\n}"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;;AAcA,MAAM,4BAAc,CAAA,GAAA,4RAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,eAAwD;QAAC,EAAE,QAAQ,EAAE;;IAChF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,OAAO;gBACT,MAAM,WAAW,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAO;gBAC5C,QAAQ,SAAS,IAAI;YACvB;QACF,EAAE,OAAO,OAAO;YACd,aAAa,UAAU,CAAC;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,WAAW;YACX,SAAS;YACT,MAAM,WAAW,MAAM,yHAAA,CAAA,aAAU,CAAC,IAAI,CAAgC,eAAe;gBACnF;gBACA;YACF;YACA,aAAa,OAAO,CAAC,SAAS,SAAS,IAAI,CAAC,KAAK;YACjD,QAAQ,SAAS,IAAI,CAAC,IAAI;YAC1B,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAY;gBACV,sBAAA;YAAT,SAAS,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,OAAO,KAAI;YAC1C,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,WAAW;YACX,MAAM,yHAAA,CAAA,aAAU,CAAC,IAAI,CAAC;YACtB,aAAa,UAAU,CAAC;YACxB,QAAQ;YACR,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,WAAW;YACX,SAAS;YACT,MAAM,WAAW,MAAM,yHAAA,CAAA,aAAU,CAAC,IAAI,CAAgC,kBAAkB;YACxF,aAAa,OAAO,CAAC,SAAS,SAAS,IAAI,CAAC,KAAK;YACjD,QAAQ,SAAS,IAAI,CAAC,IAAI;YAC1B,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAY;gBACV,sBAAA;YAAT,SAAS,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,OAAO,KAAI;YAC1C,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,WAAW;YACX,SAAS;YACT,MAAM,WAAW,MAAM,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAO,iBAAiB;YAC7D,QAAQ,SAAS,IAAI;QACvB,EAAE,OAAO,OAAY;gBACV,sBAAA;YAAT,SAAS,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,OAAO,KAAI;YAC1C,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA,iBAAiB,CAAC,CAAC;IACrB;IAEA,qBAAO,4TAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;GAnGa;;QAII,oQAAA,CAAA,YAAS;;;KAJb;AAqGN,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 732, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/app/providers.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useLayoutEffect, useState, ReactNode } from 'react';\nimport { AuthProvider } from '../contexts/AuthContext';\n\n// Theme context interface\ninterface ThemeContextType {\n  theme: string;\n  setTheme: (theme: string) => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\n// Custom hook to access theme context\nexport const useTheme = (): ThemeContextType => {\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n\n// Custom theme provider to manage light/dark mode\nconst SimpleThemeProvider: React.FC<{ children: ReactNode }> = ({ children }) => {\n  const [theme, setTheme] = useState<string>('light');\n  const [mounted, setMounted] = useState(false);\n\n  // Only access localStorage and modify DOM after component mounts on client\n  useLayoutEffect(() => {\n    const savedTheme = localStorage.getItem('medtrack-theme') || 'light';\n    setTheme(savedTheme);\n    document.documentElement.classList.toggle('dark', savedTheme === 'dark');\n    setMounted(true);\n  }, []);\n\n  const updateTheme = (newTheme: string): void => {\n    setTheme(newTheme);\n    localStorage.setItem('medtrack-theme', newTheme);\n    document.documentElement.classList.toggle('dark', newTheme === 'dark');\n  };\n\n  // Only render children after initial theme is set on client\n  if (!mounted) {\n    return null;\n  }\n\n  return (\n    <ThemeContext.Provider value={{ theme, setTheme: updateTheme }}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n\n// Providers wrapper for theme and auth\nexport function Providers({ children }: { children: ReactNode }): JSX.Element {\n  return (\n    <SimpleThemeProvider>\n      <AuthProvider>{children}</AuthProvider>\n    </SimpleThemeProvider>\n  );\n}"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAWA,MAAM,6BAAe,CAAA,GAAA,4RAAA,CAAA,gBAAa,AAAD,EAAgC;AAG1D,MAAM,WAAW;;IACtB,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAQb,kDAAkD;AAClD,MAAM,sBAAyD;QAAC,EAAE,QAAQ,EAAE;;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,2EAA2E;IAC3E,CAAA,GAAA,4RAAA,CAAA,kBAAe,AAAD;+CAAE;YACd,MAAM,aAAa,aAAa,OAAO,CAAC,qBAAqB;YAC7D,SAAS;YACT,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,eAAe;YACjE,WAAW;QACb;8CAAG,EAAE;IAEL,MAAM,cAAc,CAAC;QACnB,SAAS;QACT,aAAa,OAAO,CAAC,kBAAkB;QACvC,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,aAAa;IACjE;IAEA,4DAA4D;IAC5D,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,4TAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO,UAAU;QAAY;kBAC1D;;;;;;AAGP;IA5BM;KAAA;AA+BC,SAAS,UAAU,KAAqC;QAArC,EAAE,QAAQ,EAA2B,GAArC;IACxB,qBACE,4TAAC;kBACC,cAAA,4TAAC,kIAAA,CAAA,eAAY;sBAAE;;;;;;;;;;;AAGrB;MANgB", "debugId": null}}]}