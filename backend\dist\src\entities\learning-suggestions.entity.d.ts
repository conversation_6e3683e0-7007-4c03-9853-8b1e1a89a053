import { User } from './user.entity';
import { Material } from './materials.entity';
export declare class LearningSuggestion {
    id: string;
    user: User;
    suggestion_date: Date;
    is_viewed: boolean;
    viewed_at: Date;
    status: 'pending' | 'completed' | 'dismissed';
    completed_at: Date;
    priority: number;
    suggestions: {
        topic: string;
        priority: number;
        reason: string;
        materials: Material[];
        estimated_time: number;
        prerequisites?: string[];
        related_topics?: string[];
    }[];
    user_context: {
        recent_activity: {
            type: string;
            material_id: string;
            timestamp: Date;
        }[];
        mastery_level: {
            topic: string;
            level: number;
            last_assessed: Date;
        }[];
        learning_gaps: {
            topic: string;
            confidence: number;
            last_attempted: Date;
        }[];
    };
    feedback: {
        helpful: boolean;
        comments?: string;
        implemented?: boolean;
        timestamp?: Date;
    };
    created_at: Date;
    updated_at: Date;
}
