"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var UserFeaturesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserFeaturesService = void 0;
const common_1 = require("@nestjs/common");
const redis_service_1 = require("../redis/redis.service");
let UserFeaturesService = UserFeaturesService_1 = class UserFeaturesService {
    constructor(redisService) {
        this.redisService = redisService;
        this.logger = new common_1.Logger(UserFeaturesService_1.name);
        this.CACHE_TTL = 3600;
        this.FEATURE_CACHE_PREFIX = 'user_features:';
        this.ACTIVITY_CACHE_PREFIX = 'user_activity:';
    }
    async trackActivity(activity) {
        try {
            const activityKey = `${this.ACTIVITY_CACHE_PREFIX}${activity.userId}`;
            const recentActivities = await this.getRecentActivities(activity.userId);
            const updatedActivities = [activity, ...recentActivities.slice(0, 99)];
            await this.redisService.set(activityKey, JSON.stringify(updatedActivities), this.CACHE_TTL * 24);
            await this.updateUserFeaturesIncremental(activity);
            this.logger.debug(`Tracked activity for user ${activity.userId}: ${activity.action}`);
        }
        catch (error) {
            this.logger.error(`Failed to track activity for user ${activity.userId}:`, error);
        }
    }
    async getUserFeatureProfile(userId) {
        try {
            const cacheKey = `${this.FEATURE_CACHE_PREFIX}${userId}`;
            const cached = await this.redisService.get(cacheKey);
            if (cached) {
                return JSON.parse(cached);
            }
            const profile = await this.generateUserFeatureProfile(userId);
            if (profile) {
                await this.redisService.set(cacheKey, JSON.stringify(profile), this.CACHE_TTL);
            }
            return profile;
        }
        catch (error) {
            this.logger.error(`Failed to get user feature profile for ${userId}:`, error);
            return null;
        }
    }
    async getUserFeatureVector(userId) {
        try {
            const profile = await this.getUserFeatureProfile(userId);
            if (!profile)
                return null;
            const features = [
                profile.learningPattern.averageSessionDuration / 3600,
                profile.learningPattern.learningVelocity,
                profile.learningPattern.difficultyProgression,
                profile.learningPattern.engagementScore,
                profile.learningPattern.preferredTimeSlots.length / 24,
                profile.learningPattern.weakAreas.length,
                profile.learningPattern.strongAreas.length,
                Math.log(profile.behaviorMetrics.totalSessions + 1) / 10,
                profile.behaviorMetrics.averageScore / 100,
                profile.behaviorMetrics.completionRate,
                profile.behaviorMetrics.retentionRate,
                Math.min(profile.behaviorMetrics.streakDays / 30, 1),
                this.getDaysSinceLastActive(profile.behaviorMetrics.lastActiveDate) / 30,
                profile.contextualFeatures.socialEngagement,
                profile.contextualFeatures.helpSeekingBehavior,
                this.getTopPreferenceScore(profile.preferences.contentTypes),
                this.getPreferenceDiversity(profile.preferences.topicInterests),
            ];
            return features;
        }
        catch (error) {
            this.logger.error(`Failed to generate feature vector for ${userId}:`, error);
            return null;
        }
    }
    async getRecentActivities(userId) {
        try {
            const activityKey = `${this.ACTIVITY_CACHE_PREFIX}${userId}`;
            const cached = await this.redisService.get(activityKey);
            return cached ? JSON.parse(cached) : [];
        }
        catch (error) {
            this.logger.error(`Failed to get recent activities for ${userId}:`, error);
            return [];
        }
    }
    async updateUserFeaturesIncremental(activity) {
        const profile = await this.getUserFeatureProfile(activity.userId);
        if (!profile)
            return;
        profile.behaviorMetrics.totalSessions += 1;
        profile.behaviorMetrics.lastActiveDate = activity.timestamp;
        if (activity.score !== undefined) {
            const currentAvg = profile.behaviorMetrics.averageScore;
            const sessions = profile.behaviorMetrics.totalSessions;
            profile.behaviorMetrics.averageScore =
                (currentAvg * (sessions - 1) + activity.score) / sessions;
        }
        if (activity.contentType) {
            profile.preferences.contentTypes[activity.contentType] =
                (profile.preferences.contentTypes[activity.contentType] || 0) + 1;
        }
        const hour = activity.timestamp.getHours();
        const timeSlot = this.getTimeSlot(hour);
        profile.contextualFeatures.timePatterns[timeSlot] =
            (profile.contextualFeatures.timePatterns[timeSlot] || 0) + 1;
        profile.lastUpdated = new Date();
        const cacheKey = `${this.FEATURE_CACHE_PREFIX}${activity.userId}`;
        await this.redisService.set(cacheKey, JSON.stringify(profile), this.CACHE_TTL);
    }
    async generateUserFeatureProfile(userId) {
        const activities = await this.getRecentActivities(userId);
        if (activities.length === 0)
            return null;
        const now = new Date();
        const profile = {
            userId,
            learningPattern: this.extractLearningPattern(activities),
            behaviorMetrics: this.extractBehaviorMetrics(activities),
            preferences: this.extractPreferences(activities),
            contextualFeatures: this.extractContextualFeatures(activities),
            lastUpdated: now,
        };
        return profile;
    }
    extractLearningPattern(activities) {
        const sessions = this.groupActivitiesBySessions(activities);
        const scores = activities.filter(a => a.score !== undefined).map(a => a.score);
        return {
            preferredTimeSlots: this.getPreferredTimeSlots(activities),
            averageSessionDuration: this.calculateAverageSessionDuration(sessions),
            learningVelocity: this.calculateLearningVelocity(activities),
            difficultyProgression: this.calculateDifficultyProgression(activities),
            contentPreferences: this.getContentPreferences(activities),
            weakAreas: this.identifyWeakAreas(activities),
            strongAreas: this.identifyStrongAreas(activities),
            engagementScore: this.calculateEngagementScore(activities),
        };
    }
    extractBehaviorMetrics(activities) {
        const completedActivities = activities.filter(a => a.action === 'complete');
        const scores = activities.filter(a => a.score !== undefined).map(a => a.score);
        return {
            totalSessions: this.groupActivitiesBySessions(activities).length,
            averageScore: scores.length > 0 ? scores.reduce((a, b) => a + b) / scores.length : 0,
            completionRate: activities.length > 0 ? completedActivities.length / activities.length : 0,
            retentionRate: this.calculateRetentionRate(activities),
            streakDays: this.calculateStreakDays(activities),
            lastActiveDate: activities.length > 0 ? activities[0].timestamp : new Date(),
        };
    }
    extractPreferences(activities) {
        const contentTypes = {};
        const topicInterests = {};
        activities.forEach(activity => {
            if (activity.contentType) {
                contentTypes[activity.contentType] = (contentTypes[activity.contentType] || 0) + 1;
            }
            if (activity.metadata?.topic) {
                topicInterests[activity.metadata.topic] = (topicInterests[activity.metadata.topic] || 0) + 1;
            }
        });
        return {
            contentTypes,
            difficultyLevel: this.inferDifficultyPreference(activities),
            learningStyle: this.inferLearningStyle(activities),
            topicInterests,
        };
    }
    extractContextualFeatures(activities) {
        const timePatterns = {};
        const deviceUsage = {};
        activities.forEach(activity => {
            const timeSlot = this.getTimeSlot(activity.timestamp.getHours());
            timePatterns[timeSlot] = (timePatterns[timeSlot] || 0) + 1;
            if (activity.metadata?.device) {
                deviceUsage[activity.metadata.device] = (deviceUsage[activity.metadata.device] || 0) + 1;
            }
        });
        return {
            timePatterns,
            deviceUsage,
            socialEngagement: this.calculateSocialEngagement(activities),
            helpSeekingBehavior: this.calculateHelpSeekingBehavior(activities),
        };
    }
    getTimeSlot(hour) {
        if (hour < 6)
            return 'night';
        if (hour < 12)
            return 'morning';
        if (hour < 18)
            return 'afternoon';
        return 'evening';
    }
    groupActivitiesBySessions(activities) {
        const sessions = [];
        let currentSession = [];
        activities.forEach((activity, index) => {
            if (index === 0) {
                currentSession = [activity];
            }
            else {
                const timeDiff = activities[index - 1].timestamp.getTime() - activity.timestamp.getTime();
                if (timeDiff < 30 * 60 * 1000) {
                    currentSession.push(activity);
                }
                else {
                    sessions.push(currentSession);
                    currentSession = [activity];
                }
            }
        });
        if (currentSession.length > 0) {
            sessions.push(currentSession);
        }
        return sessions;
    }
    calculateAverageSessionDuration(sessions) {
        if (sessions.length === 0)
            return 0;
        const durations = sessions.map(session => {
            if (session.length < 2)
                return 0;
            const start = session[session.length - 1].timestamp.getTime();
            const end = session[0].timestamp.getTime();
            return (end - start) / 1000;
        });
        return durations.reduce((a, b) => a + b) / durations.length;
    }
    calculateLearningVelocity(activities) {
        const completedActivities = activities.filter(a => a.action === 'complete');
        if (completedActivities.length < 2)
            return 0;
        const timeSpan = completedActivities[0].timestamp.getTime() -
            completedActivities[completedActivities.length - 1].timestamp.getTime();
        const days = timeSpan / (1000 * 60 * 60 * 24);
        return days > 0 ? completedActivities.length / days : 0;
    }
    calculateDifficultyProgression(activities) {
        const difficultiesWithTime = activities
            .filter(a => a.metadata?.difficulty)
            .map(a => ({ difficulty: parseFloat(a.metadata.difficulty), time: a.timestamp }))
            .sort((a, b) => b.time.getTime() - a.time.getTime());
        if (difficultiesWithTime.length < 2)
            return 0;
        const recent = difficultiesWithTime.slice(0, Math.ceil(difficultiesWithTime.length / 3));
        const older = difficultiesWithTime.slice(-Math.ceil(difficultiesWithTime.length / 3));
        const recentAvg = recent.reduce((sum, item) => sum + item.difficulty, 0) / recent.length;
        const olderAvg = older.reduce((sum, item) => sum + item.difficulty, 0) / older.length;
        return recentAvg - olderAvg;
    }
    getContentPreferences(activities) {
        const preferences = {};
        activities.forEach(activity => {
            if (activity.contentType) {
                preferences[activity.contentType] = (preferences[activity.contentType] || 0) + 1;
            }
        });
        return preferences;
    }
    identifyWeakAreas(activities) {
        const topicScores = {};
        activities.forEach(activity => {
            if (activity.metadata?.topic && activity.score !== undefined) {
                if (!topicScores[activity.metadata.topic]) {
                    topicScores[activity.metadata.topic] = [];
                }
                topicScores[activity.metadata.topic].push(activity.score);
            }
        });
        return Object.entries(topicScores)
            .map(([topic, scores]) => ({
            topic,
            avgScore: scores.reduce((a, b) => a + b) / scores.length
        }))
            .filter(item => item.avgScore < 60)
            .map(item => item.topic);
    }
    identifyStrongAreas(activities) {
        const topicScores = {};
        activities.forEach(activity => {
            if (activity.metadata?.topic && activity.score !== undefined) {
                if (!topicScores[activity.metadata.topic]) {
                    topicScores[activity.metadata.topic] = [];
                }
                topicScores[activity.metadata.topic].push(activity.score);
            }
        });
        return Object.entries(topicScores)
            .map(([topic, scores]) => ({
            topic,
            avgScore: scores.reduce((a, b) => a + b) / scores.length
        }))
            .filter(item => item.avgScore > 85)
            .map(item => item.topic);
    }
    calculateEngagementScore(activities) {
        const engagementActions = activities.filter(a => ['like', 'share', 'comment', 'bookmark', 'review'].includes(a.action));
        const totalActions = activities.length;
        return totalActions > 0 ? engagementActions.length / totalActions : 0;
    }
    getPreferredTimeSlots(activities) {
        const timeSlotCounts = {};
        activities.forEach(activity => {
            const timeSlot = this.getTimeSlot(activity.timestamp.getHours());
            timeSlotCounts[timeSlot] = (timeSlotCounts[timeSlot] || 0) + 1;
        });
        const totalActivities = activities.length;
        return Object.entries(timeSlotCounts)
            .filter(([_, count]) => count / totalActivities > 0.2)
            .map(([timeSlot]) => timeSlot);
    }
    calculateRetentionRate(activities) {
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        const recentActivities = activities.filter(a => a.timestamp > sevenDaysAgo);
        return recentActivities.length > 0 ? 1 : 0;
    }
    calculateStreakDays(activities) {
        const uniqueDays = [...new Set(activities.map(a => a.timestamp.toDateString()))].sort((a, b) => new Date(b).getTime() - new Date(a).getTime());
        let streak = 0;
        const today = new Date().toDateString();
        for (let i = 0; i < uniqueDays.length; i++) {
            const dayDiff = (new Date(today).getTime() - new Date(uniqueDays[i]).getTime()) / (1000 * 60 * 60 * 24);
            if (dayDiff <= i + 1) {
                streak++;
            }
            else {
                break;
            }
        }
        return streak;
    }
    inferDifficultyPreference(activities) {
        const difficulties = activities
            .filter(a => a.metadata?.difficulty)
            .map(a => parseFloat(a.metadata.difficulty));
        if (difficulties.length === 0)
            return 'medium';
        const avgDifficulty = difficulties.reduce((a, b) => a + b) / difficulties.length;
        if (avgDifficulty < 0.3)
            return 'easy';
        if (avgDifficulty > 0.7)
            return 'hard';
        return 'medium';
    }
    inferLearningStyle(activities) {
        const actionCounts = {};
        activities.forEach(activity => {
            actionCounts[activity.action] = (actionCounts[activity.action] || 0) + 1;
        });
        const readActions = (actionCounts['read'] || 0) + (actionCounts['view'] || 0);
        const practiceActions = (actionCounts['practice'] || 0) + (actionCounts['quiz'] || 0);
        const interactiveActions = (actionCounts['interact'] || 0) + (actionCounts['discuss'] || 0);
        if (readActions > practiceActions && readActions > interactiveActions)
            return 'visual';
        if (practiceActions > interactiveActions)
            return 'kinesthetic';
        return 'interactive';
    }
    calculateSocialEngagement(activities) {
        const socialActions = activities.filter(a => ['share', 'comment', 'discuss', 'collaborate'].includes(a.action));
        return activities.length > 0 ? socialActions.length / activities.length : 0;
    }
    calculateHelpSeekingBehavior(activities) {
        const helpActions = activities.filter(a => ['help', 'hint', 'support', 'ask'].includes(a.action));
        return activities.length > 0 ? helpActions.length / activities.length : 0;
    }
    getDaysSinceLastActive(lastActive) {
        return (new Date().getTime() - lastActive.getTime()) / (1000 * 60 * 60 * 24);
    }
    getTopPreferenceScore(preferences) {
        const values = Object.values(preferences);
        if (values.length === 0)
            return 0;
        const max = Math.max(...values);
        const total = values.reduce((a, b) => a + b);
        return total > 0 ? max / total : 0;
    }
    getPreferenceDiversity(preferences) {
        const values = Object.values(preferences);
        if (values.length <= 1)
            return 0;
        const total = values.reduce((a, b) => a + b);
        if (total === 0)
            return 0;
        const probabilities = values.map(v => v / total);
        const entropy = -probabilities.reduce((sum, p) => sum + (p > 0 ? p * Math.log2(p) : 0), 0);
        return entropy / Math.log2(values.length);
    }
    async batchUpdateUserFeatures(userIds) {
        const batchSize = 10;
        for (let i = 0; i < userIds.length; i += batchSize) {
            const batch = userIds.slice(i, i + batchSize);
            await Promise.all(batch.map(async (userId) => {
                try {
                    await this.getUserFeatureProfile(userId);
                    this.logger.debug(`Updated features for user ${userId}`);
                }
                catch (error) {
                    this.logger.error(`Failed to update features for user ${userId}:`, error);
                }
            }));
        }
    }
    async getFeatureStatistics() {
        return {
            totalUsers: 0,
            averageEngagementScore: 0,
            topContentTypes: {},
            learningStyleDistribution: {},
            difficultyPreferenceDistribution: {},
        };
    }
};
exports.UserFeaturesService = UserFeaturesService;
exports.UserFeaturesService = UserFeaturesService = UserFeaturesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [redis_service_1.RedisService])
], UserFeaturesService);
//# sourceMappingURL=user-features.service.js.map