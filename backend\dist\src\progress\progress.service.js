"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProgressService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const progress_entity_1 = require("../entities/progress.entity");
let ProgressService = class ProgressService {
    constructor(progressRepository) {
        this.progressRepository = progressRepository;
    }
    async updateProgress(userId, unitId, status) {
        let progress = await this.progressRepository.findOne({
            where: { user: { id: userId }, unit: { id: unitId } }
        });
        if (!progress) {
            progress = this.progressRepository.create({
                user: { id: userId },
                unit: { id: unitId },
                status: status,
                last_accessed: new Date()
            });
        }
        else {
            progress.status = status;
            progress.last_accessed = new Date();
        }
        return this.progressRepository.save(progress);
    }
    async getProgressByUser(userId) {
        return this.progressRepository.find({
            where: { user: { id: userId } },
            relations: ['unit']
        });
    }
    async calculateOverallProgress(userId) {
        const allProgress = await this.getProgressByUser(userId);
        if (!allProgress.length)
            return 0;
        const completedUnits = allProgress.filter(p => p.status === 'complete').length;
        return (completedUnits / allProgress.length) * 100;
    }
};
exports.ProgressService = ProgressService;
exports.ProgressService = ProgressService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(progress_entity_1.Progress)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object])
], ProgressService);
//# sourceMappingURL=progress.service.js.map