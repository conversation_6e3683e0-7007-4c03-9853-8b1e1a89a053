import { Repository } from 'typeorm';
import { User } from '../../entities/user.entity';
export declare class AdminService {
    private usersRepository;
    constructor(usersRepository: Repository<User>);
    getAllUsers(): Promise<User[]>;
    getUserById(id: string): Promise<User | null>;
    createTestUser(userData: Partial<User>): Promise<User>;
    deleteUser(id: string): Promise<import("node_modules/typeorm").DeleteResult>;
    updateUser(id: string, userData: Partial<User>): Promise<User | null>;
}
