"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("../modules/auth/jwt-auth.guard");
const analytics_service_1 = require("./analytics.service");
const redis_service_1 = require("../redis/redis.service");
let AnalyticsController = class AnalyticsController {
    constructor(analyticsService, redisService) {
        this.analyticsService = analyticsService;
        this.redisService = redisService;
    }
    async trackEvent(eventData) {
        return this.analyticsService.trackEvent(eventData);
    }
    async getPerformanceMetrics(userId) {
        const cacheKey = `performance:${userId}`;
        const cachedData = await this.redisService.get(cacheKey);
        if (cachedData) {
            return JSON.parse(cachedData);
        }
        const metrics = await this.analyticsService.calculatePerformanceMetrics(userId);
        await this.redisService.set(cacheKey, JSON.stringify(metrics), 300);
        return metrics;
    }
    async getPerformancePredictions(userId) {
        const cacheKey = `predictions:${userId}`;
        const cachedData = await this.redisService.get(cacheKey);
        if (cachedData) {
            return JSON.parse(cachedData);
        }
        const predictions = await this.analyticsService.generatePredictions(userId);
        await this.redisService.set(cacheKey, JSON.stringify(predictions), 3600);
        return predictions;
    }
    async getStudyPatterns(userId) {
        return this.analyticsService.analyzeStudyPatterns(userId);
    }
    async batchTrackEvents(events) {
        return this.analyticsService.batchTrackEvents(events);
    }
    async getStudyRecommendations(userId) {
        const cacheKey = `recommendations:${userId}`;
        const cachedData = await this.redisService.get(cacheKey);
        if (cachedData) {
            return JSON.parse(cachedData);
        }
        const recommendations = await this.analyticsService.generateRecommendations(userId);
        await this.redisService.set(cacheKey, JSON.stringify(recommendations), 1800);
        return recommendations;
    }
};
exports.AnalyticsController = AnalyticsController;
__decorate([
    (0, common_1.Post)('events'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "trackEvent", null);
__decorate([
    (0, common_1.Get)('performance/:userId'),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getPerformanceMetrics", null);
__decorate([
    (0, common_1.Get)('predictions/:userId'),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getPerformancePredictions", null);
__decorate([
    (0, common_1.Get)('study-patterns/:userId'),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getStudyPatterns", null);
__decorate([
    (0, common_1.Post)('events/batch'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "batchTrackEvents", null);
__decorate([
    (0, common_1.Get)('recommendations/:userId'),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getStudyRecommendations", null);
exports.AnalyticsController = AnalyticsController = __decorate([
    (0, common_1.Controller)('analytics'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [analytics_service_1.AnalyticsService,
        redis_service_1.RedisService])
], AnalyticsController);
//# sourceMappingURL=analytics.controller.js.map