(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/app/MedTrackLayout.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_74e5b5a5._.js",
  "static/chunks/node_modules__pnpm_f85b0adf._.js",
  {
    "path": "static/chunks/src_app_components_ca368381.css",
    "included": [
      "[project]/src/app/components.css [app-client] (css)"
    ]
  },
  "static/chunks/src_app_MedTrackLayout_tsx_d1c1b1c0._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/app/MedTrackLayout.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
}]);