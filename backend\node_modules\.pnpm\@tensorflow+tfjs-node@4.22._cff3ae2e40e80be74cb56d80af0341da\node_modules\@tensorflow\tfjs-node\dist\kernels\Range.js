"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.rangeConfig = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var nodejs_kernel_backend_1 = require("../nodejs_kernel_backend");
exports.rangeConfig = {
    kernelName: tfjs_1.Range,
    backendName: 'tensorflow',
    kernelFunc: function (args) {
        var backend = args.backend;
        var _a = args.attrs, start = _a.start, stop = _a.stop, dtype = _a.dtype;
        var step = args.attrs.step;
        // TensorFlow.js specific allowances
        var sameStartStop = start === stop;
        var increasingRangeNegativeStep = start < stop && step < 0;
        var decreasingRangePositiveStep = stop < start && step > 1;
        if (sameStartStop || increasingRangeNegativeStep ||
            decreasingRangePositiveStep) {
            return (0, tfjs_1.zeros)([0], dtype);
        }
        if (stop < start && step === 1) {
            // Auto adjust the step's sign if it hasn't been set
            // (or was set to 1)
            step = -1;
        }
        var opAttrs = [(0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('Tidx', dtype)];
        var startTensor = (0, tfjs_1.scalar)(start, dtype);
        var stopTensor = (0, tfjs_1.scalar)(stop, dtype);
        var stepTensor = (0, tfjs_1.scalar)(step, dtype);
        var res = backend.executeSingleOutput(tfjs_1.Range, opAttrs, [startTensor, stopTensor, stepTensor]);
        startTensor.dispose();
        stopTensor.dispose();
        stepTensor.dispose();
        return res;
    }
};
