import { AnalyticsService } from './analytics.service';
import { RedisService } from '../redis/redis.service';
export declare class AnalyticsController {
    private readonly analyticsService;
    private readonly redisService;
    constructor(analyticsService: AnalyticsService, redisService: RedisService);
    trackEvent(eventData: {
        user_id: string;
        event_type: string;
        data: any;
        timestamp: string;
    }): Promise<import("../entities/study-event.entity").StudyEvent>;
    getPerformanceMetrics(userId: string): Promise<any>;
    getPerformancePredictions(userId: string): Promise<any>;
    getStudyPatterns(userId: string): Promise<{
        preferredStudyTimes: any;
        studyDuration: any;
        performanceByTopic: any;
        consistencyScore: number;
    }>;
    batchTrackEvents(events: Array<{
        user_id: string;
        event_type: string;
        data: any;
        timestamp: string;
    }>): Promise<import("../entities/study-event.entity").StudyEvent[]>;
    getStudyRecommendations(userId: string): Promise<any>;
}
