"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseRepository = void 0;
class BaseRepository {
    constructor(repository) {
        this.repository = repository;
    }
    async findAll() {
        return this.repository.find();
    }
    async findById(id) {
        return this.repository.findOne({ where: { id } });
    }
    async findOne(filter) {
        return this.repository.findOne({ where: filter });
    }
    async findMany(filter) {
        return this.repository.find({ where: filter });
    }
    async create(data) {
        const entity = this.repository.create(data);
        const result = await this.repository.save(entity);
        return Array.isArray(result) ? result[0] : result;
    }
    async update(id, data) {
        await this.repository.update(id, data);
        return this.findById(id);
    }
    async delete(id) {
        const result = await this.repository.delete(id);
        return (result.affected ?? 0) > 0;
    }
    async save(entity) {
        const result = await this.repository.save(entity);
        return Array.isArray(result) ? result[0] : result;
    }
}
exports.BaseRepository = BaseRepository;
//# sourceMappingURL=base.repository.interface.js.map