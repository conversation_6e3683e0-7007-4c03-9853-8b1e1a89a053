(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/auth/AuthLayout.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_46e93b35._.js",
  "static/chunks/src_components_auth_AuthLayout_tsx_a3a42fb7._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/auth/AuthLayout.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
}]);