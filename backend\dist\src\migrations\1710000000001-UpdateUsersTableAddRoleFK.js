"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateUsersTableAddRoleFK1710000000001 = void 0;
const typeorm_1 = require("typeorm");
class UpdateUsersTableAddRoleFK1710000000001 {
    async up(queryRunner) {
        await queryRunner.addColumn('users', new typeorm_1.TableColumn({
            name: 'role_id',
            type: 'uuid',
            isNullable: true,
        }));
        await queryRunner.query(`
            INSERT INTO roles (id, name, description)
            VALUES 
                (uuid_generate_v4(), 'admin', 'Administrator role'),
                (uuid_generate_v4(), 'instructor', 'Medical instructor role'),
                (uuid_generate_v4(), 'student', 'Medical student role')
            ON CONFLICT (name) DO NOTHING;
        `);
        await queryRunner.query(`
            UPDATE users u
            SET role_id = r.id
            FROM roles r
            WHERE u.role = r.name;
        `);
        await queryRunner.changeColumn('users', 'role_id', new typeorm_1.TableColumn({
            name: 'role_id',
            type: 'uuid',
            isNullable: false,
        }));
        await queryRunner.createForeignKey('users', new typeorm_1.TableForeignKey({
            name: 'FK_users_role',
            columnNames: ['role_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'roles',
            onDelete: 'RESTRICT',
        }));
        await queryRunner.dropColumn('users', 'role');
    }
    async down(queryRunner) {
        await queryRunner.addColumn('users', new typeorm_1.TableColumn({
            name: 'role',
            type: 'varchar',
            length: '20',
            default: "'student'",
        }));
        await queryRunner.query(`
            UPDATE users u
            SET role = r.name
            FROM roles r
            WHERE u.role_id = r.id;
        `);
        await queryRunner.dropForeignKey('users', 'FK_users_role');
        await queryRunner.dropColumn('users', 'role_id');
    }
}
exports.UpdateUsersTableAddRoleFK1710000000001 = UpdateUsersTableAddRoleFK1710000000001;
//# sourceMappingURL=1710000000001-UpdateUsersTableAddRoleFK.js.map