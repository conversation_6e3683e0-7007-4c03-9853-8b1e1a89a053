"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePermissionsAndUpdateRoles1710000000000 = void 0;
const typeorm_1 = require("typeorm");
class CreatePermissionsAndUpdateRoles1710000000000 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'permissions',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()',
                },
                {
                    name: 'name',
                    type: 'varchar',
                    isUnique: true,
                },
                {
                    name: 'description',
                    type: 'varchar',
                    isNullable: true,
                },
                {
                    name: 'is_active',
                    type: 'boolean',
                    default: true,
                },
                {
                    name: 'category',
                    type: 'varchar',
                    isNullable: true,
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                },
            ],
        }), true);
        await queryRunner.query(`
            ALTER TABLE roles
            ADD COLUMN IF NOT EXISTS color varchar,
            ADD COLUMN IF NOT EXISTS hierarchy_level integer,
            ADD COLUMN IF NOT EXISTS metadata jsonb;
        `);
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'role_permissions',
            columns: [
                {
                    name: 'role_id',
                    type: 'uuid',
                },
                {
                    name: 'permission_id',
                    type: 'uuid',
                },
            ],
        }), true);
        await queryRunner.createForeignKey('role_permissions', new typeorm_1.TableForeignKey({
            columnNames: ['role_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'roles',
            onDelete: 'CASCADE',
        }));
        await queryRunner.createForeignKey('role_permissions', new typeorm_1.TableForeignKey({
            columnNames: ['permission_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'permissions',
            onDelete: 'CASCADE',
        }));
        await queryRunner.query(`
            ALTER TABLE role_permissions
            ADD CONSTRAINT UQ_role_permissions UNIQUE (role_id, permission_id);
        `);
    }
    async down(queryRunner) {
        const rolePermissionsTable = await queryRunner.getTable('role_permissions');
        if (!rolePermissionsTable) {
            return;
        }
        const roleForeignKey = rolePermissionsTable.foreignKeys.find(fk => fk.columnNames.indexOf('role_id') !== -1);
        const permissionForeignKey = rolePermissionsTable.foreignKeys.find(fk => fk.columnNames.indexOf('permission_id') !== -1);
        if (roleForeignKey) {
            await queryRunner.dropForeignKey('role_permissions', roleForeignKey);
        }
        if (permissionForeignKey) {
            await queryRunner.dropForeignKey('role_permissions', permissionForeignKey);
        }
        await queryRunner.dropTable('role_permissions');
        await queryRunner.query(`
            ALTER TABLE roles
            DROP COLUMN IF EXISTS color,
            DROP COLUMN IF EXISTS hierarchy_level,
            DROP COLUMN IF EXISTS metadata;
        `);
        await queryRunner.dropTable('permissions');
    }
}
exports.CreatePermissionsAndUpdateRoles1710000000000 = CreatePermissionsAndUpdateRoles1710000000000;
//# sourceMappingURL=1710000000000-CreatePermissionsAndUpdateRoles.js.map