# Installation
> `npm install --save @types/jest`

# Summary
This package contains type definitions for jest (https://jestjs.io/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jest.

### Additional Details
 * Last updated: Wed, 23 Oct 2024 03:36:41 GMT
 * Dependencies: [expect](https://npmjs.com/package/expect), [pretty-format](https://npmjs.com/package/pretty-format)

# Credits
These definitions were written by [<PERSON><PERSON> (https://asana.com)
//                 Ivo Stratev](https://github.com/NoHomey), [jwbay](https://github.com/jwbay), [<PERSON><PERSON>](https://github.com/asvetliakov), [<PERSON>](https://github.com/alexjoverm), [<PERSON>](https://github.com/epicallan), [<PERSON><PERSON>](https://github.com/ikatyang), [<PERSON><PERSON><PERSON>](https://github.com/wsmd), [<PERSON>](https://github.com/<PERSON>), [<PERSON>](https://github.com/douglas<PERSON>teil), [Ahn](https://github.com/ahnpnl), [Jeff Lau](https://github.com/UselessPickles), [Andrew Makarov](https://github.com/r3nya), [Martin Hochel](https://github.com/hotell), [Sebastian Sebald](https://github.com/sebald), [Andy](https://github.com/andys8), [Antoine Brault](https://github.com/antoinebrault), [Gregor Stamać](https://github.com/gstamac), [ExE Boss](https://github.com/ExE-Boss), [Alex Bolenok](https://github.com/quassnoi), [Mario Beltrán Alarcón](https://github.com/Belco90), [Tony Hallett](https://github.com/tonyhallett), [Jason Yu](https://github.com/ycmjason), [Pawel Fajfer](https://github.com/pawfa), [Alexandre Germain](https://github.com/gerkindev), [Adam Jones](https://github.com/domdomegg), and [Tom Mrazauskas](https://github.com/mrazauskas).
