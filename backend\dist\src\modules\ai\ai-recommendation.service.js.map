{"version": 3, "file": "ai-recommendation.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/ai/ai-recommendation.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,qCAAyC;AACzC,4DAAkD;AAClD,sEAA2D;AAC3D,0FAA+E;AAC/E,6CAAyC;AASlC,IAAM,uBAAuB,GAA7B,MAAM,uBAAuB;IAChC,YAEqB,cAAgC,EAEhC,kBAAwC,EAExC,4BAA4D,EAC5D,SAAoB;QALpB,mBAAc,GAAd,cAAc,CAAkB;QAEhC,uBAAkB,GAAlB,kBAAkB,CAAsB;QAExC,iCAA4B,GAA5B,4BAA4B,CAAgC;QAC5D,cAAS,GAAT,SAAS,CAAW;IACtC,CAAC;IAEJ,KAAK,CAAC,8BAA8B,CAAC,MAAc;QAE/C,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAGxE,OAAO,eAAe,CAAC,GAAG,CAAC,CAAC,GAAmB,EAAE,EAAE,CAAC,CAAC;YACjD,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,KAAK,EAAE,GAAG,CAAC,KAAK;YAChB,MAAM,EAAE,GAAG,CAAC,MAAM;SACrB,CAAC,CAAC,CAAC;IACR,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,MAAc;QACxC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;QAC1E,IAAI,CAAC,IAAI;YAAE,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;QAG7C,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;QAC9D,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAG9D,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAGxE,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAC5D,KAAK,EAAE;gBACH,EAAE,EAAE,IAAA,YAAE,EAAC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAmB,EAAE,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;aACvE;YACD,SAAS,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;SAChC,CAAC,CAAC;QAEH,OAAO,oBAAoB,CAAC;IAChC,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,MAAc;QAE/C,OAAO,EAAE,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,MAAc;QAE3C,OAAO,EAAE,CAAC;IACd,CAAC;CACJ,CAAA;AAtDY,0DAAuB;kCAAvB,uBAAuB;IADnC,IAAA,mBAAU,GAAE;IAGJ,WAAA,IAAA,0BAAgB,EAAC,kBAAI,CAAC,CAAA;IAEtB,WAAA,IAAA,0BAAgB,EAAC,2BAAQ,CAAC,CAAA;IAE1B,WAAA,IAAA,0BAAgB,EAAC,+CAAkB,CAAC,CAAA;qCAHJ,oBAAU;QAEN,oBAAU;QAEA,oBAAU;QAC7B,sBAAS;GARhC,uBAAuB,CAsDnC"}