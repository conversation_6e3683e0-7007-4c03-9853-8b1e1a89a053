{"version": 3, "file": "1720000000000-AddUnitQuizGamificationTables.js", "sourceRoot": "", "sources": ["../../../../src/database/migrations/1720000000000-AddUnitQuizGamificationTables.ts"], "names": [], "mappings": ";;;AAAA,qCAAkF;AAElF,MAAa,0CAA0C;IAC9C,KAAK,CAAC,EAAE,CAAC,WAAwB;QAEtC,MAAM,WAAW,CAAC,WAAW,CAAC,IAAI,eAAK,CAAC;YACtC,IAAI,EAAE,kBAAkB;YACxB,OAAO,EAAE;gBACP,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE;gBAC5F,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE;gBAChC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE;gBAChC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE;gBAC9B,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE;gBACnD,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE;gBAC1D,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE;aAC3D;YACD,OAAO,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC;SACjD,CAAC,CAAC,CAAC;QACJ,MAAM,WAAW,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,yBAAe,CAAC;YACzE,WAAW,EAAE,CAAC,QAAQ,CAAC;YACvB,qBAAqB,EAAE,CAAC,IAAI,CAAC;YAC7B,mBAAmB,EAAE,OAAO;YAC5B,QAAQ,EAAE,SAAS;SACpB,CAAC,CAAC,CAAC;QACJ,MAAM,WAAW,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,yBAAe,CAAC;YACzE,WAAW,EAAE,CAAC,QAAQ,CAAC;YACvB,qBAAqB,EAAE,CAAC,IAAI,CAAC;YAC7B,mBAAmB,EAAE,OAAO;YAC5B,QAAQ,EAAE,SAAS;SACpB,CAAC,CAAC,CAAC;QAGJ,MAAM,WAAW,CAAC,WAAW,CAAC,IAAI,eAAK,CAAC;YACtC,IAAI,EAAE,SAAS;YACf,OAAO,EAAE;gBACP,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE;gBAC5F,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE;gBAChC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE;gBAC/B,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE;aAC3D;SACF,CAAC,CAAC,CAAC;QACJ,MAAM,WAAW,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,yBAAe,CAAC;YAChE,WAAW,EAAE,CAAC,QAAQ,CAAC;YACvB,qBAAqB,EAAE,CAAC,IAAI,CAAC;YAC7B,mBAAmB,EAAE,OAAO;YAC5B,QAAQ,EAAE,SAAS;SACpB,CAAC,CAAC,CAAC;QAGJ,MAAM,WAAW,CAAC,WAAW,CAAC,IAAI,eAAK,CAAC;YACtC,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE;gBACP,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE;gBAC5F,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE;gBAChC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE;gBACtC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE;aAC3D;SACF,CAAC,CAAC,CAAC;QACJ,MAAM,WAAW,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,yBAAe,CAAC;YAC/D,WAAW,EAAE,CAAC,QAAQ,CAAC;YACvB,qBAAqB,EAAE,CAAC,IAAI,CAAC;YAC7B,mBAAmB,EAAE,OAAO;YAC5B,QAAQ,EAAE,SAAS;SACpB,CAAC,CAAC,CAAC;QAGJ,MAAM,WAAW,CAAC,WAAW,CAAC,IAAI,eAAK,CAAC;YACtC,IAAI,EAAE,SAAS;YACf,OAAO,EAAE;gBACP,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE;gBAC5F,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE;gBAChC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE;gBAClD,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE;aAC7D;SACF,CAAC,CAAC,CAAC;QACJ,MAAM,WAAW,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,yBAAe,CAAC;YAChE,WAAW,EAAE,CAAC,QAAQ,CAAC;YACvB,qBAAqB,EAAE,CAAC,IAAI,CAAC;YAC7B,mBAAmB,EAAE,OAAO;YAC5B,QAAQ,EAAE,SAAS;SACpB,CAAC,CAAC,CAAC;IACN,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QACvC,MAAM,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACtC,MAAM,WAAW,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QACvC,MAAM,WAAW,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;IAClD,CAAC;CACF;AAxFD,gGAwFC"}