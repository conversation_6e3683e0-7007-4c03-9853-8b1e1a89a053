"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var RedisService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const ioredis_1 = __importDefault(require("ioredis"));
let RedisService = RedisService_1 = class RedisService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(RedisService_1.name);
        this.memoryStore = new Map();
        this.isConnected = false;
        this.redis = new ioredis_1.default({
            host: this.configService.get('redis.host'),
            port: this.configService.get('redis.port'),
            password: this.configService.get('redis.password'),
            db: this.configService.get('redis.db'),
            keyPrefix: this.configService.get('redis.keyPrefix'),
            retryStrategy: this.configService.get('redis.retryStrategy'),
            maxRetriesPerRequest: this.configService.get('redis.maxRetriesPerRequest'),
            enableOfflineQueue: this.configService.get('redis.enableOfflineQueue'),
        });
        this.redis.on('connect', () => {
            this.isConnected = true;
            this.logger.log('Connected to Redis');
        });
        this.redis.on('error', (error) => {
            this.isConnected = false;
            this.logger.warn('Redis connection error, using in-memory fallback:', error.message);
        });
        setInterval(() => {
            const now = Date.now();
            for (const [key, data] of this.memoryStore.entries()) {
                if (data.expiry && data.expiry < now) {
                    this.memoryStore.delete(key);
                }
            }
        }, 60000);
    }
    async getFromMemory(key) {
        const data = this.memoryStore.get(key);
        if (!data)
            return null;
        if (data.expiry && data.expiry < Date.now()) {
            this.memoryStore.delete(key);
            return null;
        }
        return data.value;
    }
    setInMemory(key, value, ttlSeconds) {
        const data = { value };
        if (ttlSeconds) {
            data.expiry = Date.now() + (ttlSeconds * 1000);
        }
        this.memoryStore.set(key, data);
    }
    async get(key) {
        if (this.isConnected) {
            try {
                return await this.redis.get(key);
            }
            catch (error) {
                this.logger.warn('Redis get failed, falling back to memory:', error.message);
            }
        }
        return this.getFromMemory(key);
    }
    async set(key, value, ttlSeconds) {
        this.setInMemory(key, value, ttlSeconds);
        if (this.isConnected) {
            try {
                if (ttlSeconds) {
                    await this.redis.setex(key, ttlSeconds, value);
                }
                else {
                    await this.redis.set(key, value);
                }
            }
            catch (error) {
                this.logger.warn('Redis set failed, using memory only:', error.message);
            }
        }
    }
    async del(key) {
        this.memoryStore.delete(key);
        if (this.isConnected) {
            try {
                await this.redis.del(key);
            }
            catch (error) {
                this.logger.warn('Redis del failed, using memory only:', error.message);
            }
        }
    }
    async exists(key) {
        if (this.isConnected) {
            try {
                const result = await this.redis.exists(key);
                return result === 1;
            }
            catch (error) {
                this.logger.warn('Redis exists failed, falling back to memory:', error.message);
            }
        }
        return this.memoryStore.has(key);
    }
    async onApplicationShutdown() {
        if (this.isConnected) {
            await this.redis.quit();
        }
    }
};
exports.RedisService = RedisService;
exports.RedisService = RedisService = RedisService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], RedisService);
//# sourceMappingURL=redis.service.js.map