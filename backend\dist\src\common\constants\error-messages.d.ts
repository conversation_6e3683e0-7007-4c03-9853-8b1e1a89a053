export declare const ErrorMessages: {
    readonly AUTH: {
        readonly INVALID_CREDENTIALS: "Invalid username or password";
        readonly EMAIL_EXISTS: "Email is already registered";
        readonly USERNAME_EXISTS: "Username is already taken";
        readonly PASSWORD_REQUIREMENTS: {
            readonly LENGTH: "Password must be at least 10 characters long";
            readonly UPPERCASE: "Password must contain at least one uppercase letter";
            readonly LOWERCASE: "Password must contain at least one lowercase letter";
            readonly NUMBER: "Password must contain at least one number";
            readonly SPECIAL: "Password must contain at least one special character (!@#$%^&*)";
        };
        readonly TOKEN_INVALID: "Invalid or expired token";
        readonly TOKEN_MISSING: "Authentication token is required";
        readonly UNAUTHORIZED: "You are not authorized to perform this action";
    };
    readonly VALIDATION: {
        readonly REQUIRED: (field: string) => string;
        readonly INVALID_FORMAT: (field: string) => string;
        readonly MIN_LENGTH: (field: string, length: number) => string;
        readonly MAX_LENGTH: (field: string, length: number) => string;
    };
    readonly SERVER: {
        readonly INTERNAL_ERROR: "An internal server error occurred";
        readonly SERVICE_UNAVAILABLE: "Service is temporarily unavailable";
        readonly DATABASE_ERROR: "Database operation failed";
    };
    readonly USER: {
        readonly NOT_FOUND: "User not found";
        readonly INACTIVE: "User account is inactive";
        readonly ALREADY_EXISTS: "User already exists";
    };
};
