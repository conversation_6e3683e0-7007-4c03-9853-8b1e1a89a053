import { User } from './user.entity';
import { Material } from './materials.entity';
export declare class LearningSuggestion {
    id: string;
    title: string;
    description: string;
    priority: number;
    is_completed: boolean;
    completion_date: Date;
    metadata: {
        estimated_time?: number;
        prerequisites?: string[];
        related_topics?: string[];
        difficulty?: string;
    };
    user: User;
    material: Material;
    createdAt: Date;
    updatedAt: Date;
}
