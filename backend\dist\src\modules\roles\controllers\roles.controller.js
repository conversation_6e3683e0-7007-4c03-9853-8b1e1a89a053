"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RolesController = void 0;
const common_1 = require("@nestjs/common");
const roles_guards_1 = require("../../../common/guards/roles.guards");
const roles_decorator_1 = require("../../../common/decorators/roles.decorator");
const role_initialization_service_1 = require("../services/role-initialization.service");
const role_entity_1 = require("../../../entities/role.entity");
const permission_entity_1 = require("../../../entities/permission.entity");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
let RolesController = class RolesController {
    constructor(roleRepository, permissionRepository, roleInitializationService) {
        this.roleRepository = roleRepository;
        this.permissionRepository = permissionRepository;
        this.roleInitializationService = roleInitializationService;
    }
    async getAllRoles() {
        return this.roleRepository.find({
            relations: ['permissions'],
            order: {
                hierarchy_level: 'ASC'
            }
        });
    }
    async getRole(id) {
        return this.roleRepository.findOne({
            where: { id },
            relations: ['permissions']
        });
    }
    async createRole(roleData) {
        const role = this.roleRepository.create(roleData);
        return this.roleRepository.save(role);
    }
    async updateRole(id, roleData) {
        await this.roleRepository.update(id, roleData);
        return this.roleRepository.findOne({
            where: { id },
            relations: ['permissions']
        });
    }
    async deleteRole(id) {
        await this.roleRepository.update(id, { is_active: false });
        return { message: 'Role deactivated successfully' };
    }
    async initializeRoles() {
        await this.roleInitializationService.initializeDefaultRoles();
        return { message: 'Default roles initialized successfully' };
    }
    async getAllPermissions() {
        return this.permissionRepository.find({
            order: {
                category: 'ASC',
                name: 'ASC'
            }
        });
    }
    async createPermission(permissionData) {
        const permission = this.permissionRepository.create(permissionData);
        return this.permissionRepository.save(permission);
    }
    async updatePermission(id, permissionData) {
        await this.permissionRepository.update(id, permissionData);
        return this.permissionRepository.findOne({
            where: { id }
        });
    }
    async deletePermission(id) {
        await this.permissionRepository.update(id, { is_active: false });
        return { message: 'Permission deactivated successfully' };
    }
};
exports.RolesController = RolesController;
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)('admin'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], RolesController.prototype, "getAllRoles", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)('admin'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], RolesController.prototype, "getRole", null);
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)('admin'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], RolesController.prototype, "createRole", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, roles_decorator_1.Roles)('admin'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], RolesController.prototype, "updateRole", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)('admin'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], RolesController.prototype, "deleteRole", null);
__decorate([
    (0, common_1.Post)('initialize'),
    (0, roles_decorator_1.Roles)('admin'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], RolesController.prototype, "initializeRoles", null);
__decorate([
    (0, common_1.Get)('permissions'),
    (0, roles_decorator_1.Roles)('admin'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], RolesController.prototype, "getAllPermissions", null);
__decorate([
    (0, common_1.Post)('permissions'),
    (0, roles_decorator_1.Roles)('admin'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], RolesController.prototype, "createPermission", null);
__decorate([
    (0, common_1.Put)('permissions/:id'),
    (0, roles_decorator_1.Roles)('admin'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], RolesController.prototype, "updatePermission", null);
__decorate([
    (0, common_1.Delete)('permissions/:id'),
    (0, roles_decorator_1.Roles)('admin'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], RolesController.prototype, "deletePermission", null);
exports.RolesController = RolesController = __decorate([
    (0, common_1.Controller)('roles'),
    (0, common_1.UseGuards)(roles_guards_1.RolesGuard),
    __param(0, (0, typeorm_1.InjectRepository)(role_entity_1.Role)),
    __param(1, (0, typeorm_1.InjectRepository)(permission_entity_1.Permission)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        role_initialization_service_1.RoleInitializationService])
], RolesController);
//# sourceMappingURL=roles.controller.js.map