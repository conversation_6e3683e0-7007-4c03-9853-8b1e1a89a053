"use strict";
/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.nodeHTTPRequest = exports.fileSystem = void 0;
/**
 * Public exports from the `io` module.
 */
var file_system_1 = require("./file_system");
Object.defineProperty(exports, "fileSystem", { enumerable: true, get: function () { return file_system_1.fileSystem; } });
var node_http_1 = require("./node_http");
Object.defineProperty(exports, "nodeHTTPRequest", { enumerable: true, get: function () { return node_http_1.nodeHTTPRequest; } });
