#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@swc+cli@0.6.0_@swc+core@1.11.16_chokidar@4.0.3/node_modules/@swc/cli/bin/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@swc+cli@0.6.0_@swc+core@1.11.16_chokidar@4.0.3/node_modules/@swc/cli/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@swc+cli@0.6.0_@swc+core@1.11.16_chokidar@4.0.3/node_modules/@swc/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@swc+cli@0.6.0_@swc+core@1.11.16_chokidar@4.0.3/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@swc+cli@0.6.0_@swc+core@1.11.16_chokidar@4.0.3/node_modules/@swc/cli/bin/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@swc+cli@0.6.0_@swc+core@1.11.16_chokidar@4.0.3/node_modules/@swc/cli/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@swc+cli@0.6.0_@swc+core@1.11.16_chokidar@4.0.3/node_modules/@swc/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@swc+cli@0.6.0_@swc+core@1.11.16_chokidar@4.0.3/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@swc/cli/bin/swcx.js" "$@"
else
  exec node  "$basedir/../@swc/cli/bin/swcx.js" "$@"
fi
