declare const _default: (() => {
    host: string;
    port: number;
    password: string | undefined;
    db: number;
    keyPrefix: string;
    retryStrategy: (times: number) => number;
    maxRetriesPerRequest: number;
    enableOfflineQueue: boolean;
}) & import("node_modules/@nestjs/config").ConfigFactoryKeyHost<{
    host: string;
    port: number;
    password: string | undefined;
    db: number;
    keyPrefix: string;
    retryStrategy: (times: number) => number;
    maxRetriesPerRequest: number;
    enableOfflineQueue: boolean;
}>;
export default _default;
