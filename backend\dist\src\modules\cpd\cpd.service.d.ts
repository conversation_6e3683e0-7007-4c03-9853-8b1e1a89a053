import { Repository } from 'typeorm';
import { CPDActivity, CPDCycle, CPDActivityType } from '../../entities/cpd-tracking.entity';
import { User } from '../../entities/user.entity';
import { Material } from '../../entities/materials.entity';
export declare class CPDService {
    private cpdActivityRepository;
    private cpdCycleRepository;
    private userRepository;
    private materialRepository;
    constructor(cpdActivityRepository: Repository<CPDActivity>, cpdCycleRepository: Repository<CPDCycle>, userRepository: Repository<User>, materialRepository: Repository<Material>);
    createCPDActivity(userId: string, data: {
        activityType: CPDActivityType;
        points: number;
        description: string;
        title: string;
        materialId?: string;
        metadata?: any;
    }): Promise<CPDActivity>;
    getCurrentCPDCycle(userId: string): Promise<CPDCycle>;
    getCPDActivities(userId: string, options?: {
        startDate?: Date;
        endDate?: Date;
        activityType?: string;
        isVerified?: boolean;
    }): Promise<CPDActivity[]>;
    verifyCPDActivity(activityId: string, verified: boolean, notes?: string): Promise<CPDActivity>;
    updateCPDCycle(cycleId: string, data: {
        required_points?: number;
    }): Promise<CPDCycle>;
}
