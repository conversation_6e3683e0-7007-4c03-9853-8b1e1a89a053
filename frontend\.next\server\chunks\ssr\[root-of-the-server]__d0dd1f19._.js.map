{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/components/dashboard/MedicalEducationDashboard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const MedicalEducationDashboard = registerClientReference(\n    function() { throw new Error(\"Attempted to call MedicalEducationDashboard() from the server but MedicalEducationDashboard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/MedicalEducationDashboard.tsx <module evaluation>\",\n    \"MedicalEducationDashboard\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,4BAA4B,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EAC3D;IAAa,MAAM,IAAI,MAAM;AAAkQ,GAC/R,wFACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/components/dashboard/MedicalEducationDashboard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const MedicalEducationDashboard = registerClientReference(\n    function() { throw new Error(\"Attempted to call MedicalEducationDashboard() from the server but MedicalEducationDashboard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/MedicalEducationDashboard.tsx\",\n    \"MedicalEducationDashboard\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,4BAA4B,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EAC3D;IAAa,MAAM,IAAI,MAAM;AAAkQ,GAC/R,oEACA", "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/services/rateLimiter.ts"], "sourcesContent": ["interface RateLimitConfig {\r\n  maxRequests: number;\r\n  timeWindow: number; // in milliseconds\r\n}\r\n\r\ninterface RateLimitState {\r\n  requests: number;\r\n  resetTime: number;\r\n}\r\n\r\nclass RateLimiter {\r\n  private static instance: RateLimiter;\r\n  private limits: Map<string, RateLimitConfig>;\r\n  private states: Map<string, RateLimitState>;\r\n\r\n  private constructor() {\r\n    this.limits = new Map();\r\n    this.states = new Map();\r\n  }\r\n\r\n  static getInstance(): RateLimiter {\r\n    if (!RateLimiter.instance) {\r\n      RateLimiter.instance = new RateLimiter();\r\n    }\r\n    return RateLimiter.instance;\r\n  }\r\n\r\n  setLimit(endpoint: string, config: RateLimitConfig): void {\r\n    this.limits.set(endpoint, config);\r\n  }\r\n\r\n  async checkLimit(endpoint: string): Promise<boolean> {\r\n    const config = this.limits.get(endpoint);\r\n    if (!config) return true; // No limit set for this endpoint\r\n\r\n    const now = Date.now();\r\n    const state = this.states.get(endpoint) || { requests: 0, resetTime: now + config.timeWindow };\r\n\r\n    // Reset if time window has passed\r\n    if (now > state.resetTime) {\r\n      state.requests = 0;\r\n      state.resetTime = now + config.timeWindow;\r\n    }\r\n\r\n    // Check if limit is exceeded\r\n    if (state.requests >= config.maxRequests) {\r\n      return false;\r\n    }\r\n\r\n    // Increment request count\r\n    state.requests++;\r\n    this.states.set(endpoint, state);\r\n    return true;\r\n  }\r\n\r\n  getRemainingRequests(endpoint: string): number {\r\n    const config = this.limits.get(endpoint);\r\n    if (!config) return Infinity;\r\n\r\n    const state = this.states.get(endpoint);\r\n    if (!state) return config.maxRequests;\r\n\r\n    return Math.max(0, config.maxRequests - state.requests);\r\n  }\r\n\r\n  getResetTime(endpoint: string): number {\r\n    const state = this.states.get(endpoint);\r\n    return state?.resetTime || Date.now();\r\n  }\r\n\r\n  reset(endpoint: string): void {\r\n    this.states.delete(endpoint);\r\n  }\r\n}\r\n\r\nexport const rateLimiter = RateLimiter.getInstance();\r\n\r\n// Default rate limits\r\nrateLimiter.setLimit('auth/login', { maxRequests: 5, timeWindow: 60000 }); // 5 requests per minute\r\nrateLimiter.setLimit('auth/register', { maxRequests: 3, timeWindow: 3600000 }); // 3 requests per hour\r\nrateLimiter.setLimit('auth/forgot-password', { maxRequests: 3, timeWindow: 3600000 }); // 3 requests per hour\r\nrateLimiter.setLimit('api/*', { maxRequests: 100, timeWindow: 60000 }); // 100 requests per minute for general API "], "names": [], "mappings": ";;;AAUA,MAAM;IACJ,OAAe,SAAsB;IAC7B,OAAqC;IACrC,OAAoC;IAE5C,aAAsB;QACpB,IAAI,CAAC,MAAM,GAAG,IAAI;QAClB,IAAI,CAAC,MAAM,GAAG,IAAI;IACpB;IAEA,OAAO,cAA2B;QAChC,IAAI,CAAC,YAAY,QAAQ,EAAE;YACzB,YAAY,QAAQ,GAAG,IAAI;QAC7B;QACA,OAAO,YAAY,QAAQ;IAC7B;IAEA,SAAS,QAAgB,EAAE,MAAuB,EAAQ;QACxD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU;IAC5B;IAEA,MAAM,WAAW,QAAgB,EAAoB;QACnD,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;QAC/B,IAAI,CAAC,QAAQ,OAAO,MAAM,iCAAiC;QAE3D,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa;YAAE,UAAU;YAAG,WAAW,MAAM,OAAO,UAAU;QAAC;QAE7F,kCAAkC;QAClC,IAAI,MAAM,MAAM,SAAS,EAAE;YACzB,MAAM,QAAQ,GAAG;YACjB,MAAM,SAAS,GAAG,MAAM,OAAO,UAAU;QAC3C;QAEA,6BAA6B;QAC7B,IAAI,MAAM,QAAQ,IAAI,OAAO,WAAW,EAAE;YACxC,OAAO;QACT;QAEA,0BAA0B;QAC1B,MAAM,QAAQ;QACd,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU;QAC1B,OAAO;IACT;IAEA,qBAAqB,QAAgB,EAAU;QAC7C,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;QAC/B,IAAI,CAAC,QAAQ,OAAO;QAEpB,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;QAC9B,IAAI,CAAC,OAAO,OAAO,OAAO,WAAW;QAErC,OAAO,KAAK,GAAG,CAAC,GAAG,OAAO,WAAW,GAAG,MAAM,QAAQ;IACxD;IAEA,aAAa,QAAgB,EAAU;QACrC,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;QAC9B,OAAO,OAAO,aAAa,KAAK,GAAG;IACrC;IAEA,MAAM,QAAgB,EAAQ;QAC5B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IACrB;AACF;AAEO,MAAM,cAAc,YAAY,WAAW;AAElD,sBAAsB;AACtB,YAAY,QAAQ,CAAC,cAAc;IAAE,aAAa;IAAG,YAAY;AAAM,IAAI,wBAAwB;AACnG,YAAY,QAAQ,CAAC,iBAAiB;IAAE,aAAa;IAAG,YAAY;AAAQ,IAAI,sBAAsB;AACtG,YAAY,QAAQ,CAAC,wBAAwB;IAAE,aAAa;IAAG,YAAY;AAAQ,IAAI,sBAAsB;AAC7G,YAAY,QAAQ,CAAC,SAAS;IAAE,aAAa;IAAK,YAAY;AAAM,IAAI,2CAA2C", "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\nimport { ApiError, ApiResponse, RequestOptions } from '@/types/api';\nimport { rateLimiter } from './rateLimiter';\nimport Cookies from 'js-cookie';\n\nclass ApiService {\n  private static instance: ApiService;\n  private api: AxiosInstance;\n\n  private constructor() {\n    this.api = axios.create({\n      baseURL: process.env.NEXT_PUBLIC_API_URL,\n      timeout: 30000,\n      withCredentials: true,\n    });\n\n    this.setupInterceptors();\n  }\n\n  static getInstance(): ApiService {\n    if (!ApiService.instance) {\n      ApiService.instance = new ApiService();\n    }\n    return ApiService.instance;\n  }\n\n  private setupInterceptors(): void {\n    // Request interceptor\n    this.api.interceptors.request.use(\n      async (config) => {\n        try {\n          // Check rate limit\n          const endpoint = config.url?.replace(/^\\//, '') || '';\n          const isAllowed = await rateLimiter.checkLimit(endpoint);\n          \n          if (!isAllowed) {\n            const resetTime = rateLimiter.getResetTime(endpoint);\n            const remainingTime = Math.ceil((resetTime - Date.now()) / 1000);\n            throw new Error(`Rate limit exceeded. Please try again in ${remainingTime} seconds.`);\n          }\n\n          const token = Cookies.get('access_token');\n          if (token) {\n            config.headers.Authorization = `Bearer ${token}`;\n          }\n          return config;\n        } catch (error) {\n          return Promise.reject(error);\n        }\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // Response interceptor\n    this.api.interceptors.response.use(\n      (response) => response,\n      async (error) => {\n        const originalRequest = error.config;\n\n        // Handle token refresh\n        if (error.response?.status === 401 && !originalRequest._retry) {\n          originalRequest._retry = true;\n\n          try {\n            await authService.getAccessToken();\n            return this.api(originalRequest);\n          } catch (refreshError) {\n            return Promise.reject(refreshError);\n          }\n        }\n\n        // Transform error response\n        const apiError: ApiError = {\n          code: error.response?.data?.code || 'UNKNOWN_ERROR',\n          message: error.response?.data?.message || error.message || 'An unexpected error occurred',\n          details: error.response?.data?.details,\n          status: error.response?.status || 500,\n        };\n\n        return Promise.reject(apiError);\n      }\n    );\n  }\n\n  async get<T>(url: string, options?: RequestOptions): Promise<ApiResponse<T>> {\n    const config: AxiosRequestConfig = {\n      headers: options?.headers,\n      params: options?.params,\n      timeout: options?.timeout,\n      withCredentials: options?.withCredentials,\n    };\n\n    const response: AxiosResponse<ApiResponse<T>> = await this.api.get(url, config);\n    return response.data;\n  }\n\n  async post<T>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {\n    const config: AxiosRequestConfig = {\n      headers: options?.headers,\n      params: options?.params,\n      timeout: options?.timeout,\n      withCredentials: options?.withCredentials,\n    };\n\n    const response: AxiosResponse<ApiResponse<T>> = await this.api.post(url, data, config);\n    return response.data;\n  }\n\n  async put<T>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {\n    const config: AxiosRequestConfig = {\n      headers: options?.headers,\n      params: options?.params,\n      timeout: options?.timeout,\n      withCredentials: options?.withCredentials,\n    };\n\n    const response: AxiosResponse<ApiResponse<T>> = await this.api.put(url, data, config);\n    return response.data;\n  }\n\n  async patch<T>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {\n    const config: AxiosRequestConfig = {\n      headers: options?.headers,\n      params: options?.params,\n      timeout: options?.timeout,\n      withCredentials: options?.withCredentials,\n    };\n\n    const response: AxiosResponse<ApiResponse<T>> = await this.api.patch(url, data, config);\n    return response.data;\n  }\n\n  async delete<T>(url: string, options?: RequestOptions): Promise<ApiResponse<T>> {\n    const config: AxiosRequestConfig = {\n      headers: options?.headers,\n      params: options?.params,\n      timeout: options?.timeout,\n      withCredentials: options?.withCredentials,\n    };\n\n    const response: AxiosResponse<ApiResponse<T>> = await this.api.delete(url, config);\n    return response.data;\n  }\n}\n\nconst apiService = ApiService.getInstance();\nexport { apiService };\nexport default apiService;\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;;;;AAEA,MAAM;IACJ,OAAe,SAAqB;IAC5B,IAAmB;IAE3B,aAAsB;QACpB,IAAI,CAAC,GAAG,GAAG,wLAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YACtB,OAAO;YACP,SAAS;YACT,iBAAiB;QACnB;QAEA,IAAI,CAAC,iBAAiB;IACxB;IAEA,OAAO,cAA0B;QAC/B,IAAI,CAAC,WAAW,QAAQ,EAAE;YACxB,WAAW,QAAQ,GAAG,IAAI;QAC5B;QACA,OAAO,WAAW,QAAQ;IAC5B;IAEQ,oBAA0B;QAChC,sBAAsB;QACtB,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAC/B,OAAO;YACL,IAAI;gBACF,mBAAmB;gBACnB,MAAM,WAAW,OAAO,GAAG,EAAE,QAAQ,OAAO,OAAO;gBACnD,MAAM,YAAY,MAAM,8HAAA,CAAA,cAAW,CAAC,UAAU,CAAC;gBAE/C,IAAI,CAAC,WAAW;oBACd,MAAM,YAAY,8HAAA,CAAA,cAAW,CAAC,YAAY,CAAC;oBAC3C,MAAM,gBAAgB,KAAK,IAAI,CAAC,CAAC,YAAY,KAAK,GAAG,EAAE,IAAI;oBAC3D,MAAM,IAAI,MAAM,CAAC,yCAAyC,EAAE,cAAc,SAAS,CAAC;gBACtF;gBAEA,MAAM,QAAQ,8MAAA,CAAA,UAAO,CAAC,GAAG,CAAC;gBAC1B,IAAI,OAAO;oBACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;gBAClD;gBACA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,OAAO,QAAQ,MAAM,CAAC;YACxB;QACF,GACA,CAAC;YACC,OAAO,QAAQ,MAAM,CAAC;QACxB;QAGF,uBAAuB;QACvB,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAChC,CAAC,WAAa,UACd,OAAO;YACL,MAAM,kBAAkB,MAAM,MAAM;YAEpC,uBAAuB;YACvB,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,CAAC,gBAAgB,MAAM,EAAE;gBAC7D,gBAAgB,MAAM,GAAG;gBAEzB,IAAI;oBACF,MAAM,YAAY,cAAc;oBAChC,OAAO,IAAI,CAAC,GAAG,CAAC;gBAClB,EAAE,OAAO,cAAc;oBACrB,OAAO,QAAQ,MAAM,CAAC;gBACxB;YACF;YAEA,2BAA2B;YAC3B,MAAM,WAAqB;gBACzB,MAAM,MAAM,QAAQ,EAAE,MAAM,QAAQ;gBACpC,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;gBAC3D,SAAS,MAAM,QAAQ,EAAE,MAAM;gBAC/B,QAAQ,MAAM,QAAQ,EAAE,UAAU;YACpC;YAEA,OAAO,QAAQ,MAAM,CAAC;QACxB;IAEJ;IAEA,MAAM,IAAO,GAAW,EAAE,OAAwB,EAA2B;QAC3E,MAAM,SAA6B;YACjC,SAAS,SAAS;YAClB,QAAQ,SAAS;YACjB,SAAS,SAAS;YAClB,iBAAiB,SAAS;QAC5B;QAEA,MAAM,WAA0C,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK;QACxE,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,KAAQ,GAAW,EAAE,IAAU,EAAE,OAAwB,EAA2B;QACxF,MAAM,SAA6B;YACjC,SAAS,SAAS;YAClB,QAAQ,SAAS;YACjB,SAAS,SAAS;YAClB,iBAAiB,SAAS;QAC5B;QAEA,MAAM,WAA0C,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,MAAM;QAC/E,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,IAAO,GAAW,EAAE,IAAU,EAAE,OAAwB,EAA2B;QACvF,MAAM,SAA6B;YACjC,SAAS,SAAS;YAClB,QAAQ,SAAS;YACjB,SAAS,SAAS;YAClB,iBAAiB,SAAS;QAC5B;QAEA,MAAM,WAA0C,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,MAAM;QAC9E,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,MAAS,GAAW,EAAE,IAAU,EAAE,OAAwB,EAA2B;QACzF,MAAM,SAA6B;YACjC,SAAS,SAAS;YAClB,QAAQ,SAAS;YACjB,SAAS,SAAS;YAClB,iBAAiB,SAAS;QAC5B;QAEA,MAAM,WAA0C,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,MAAM;QAChF,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,OAAU,GAAW,EAAE,OAAwB,EAA2B;QAC9E,MAAM,SAA6B;YACjC,SAAS,SAAS;YAClB,QAAQ,SAAS;YACjB,SAAS,SAAS;YAClB,iBAAiB,SAAS;QAC5B;QAEA,MAAM,WAA0C,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK;QAC3E,OAAO,SAAS,IAAI;IACtB;AACF;AAEA,MAAM,aAAa,WAAW,WAAW;;uCAE1B", "debugId": null}}, {"offset": {"line": 360, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { apiService } from '@/services/api';\r\nimport { User } from '@/types';\r\n\r\ninterface AuthContextType {\r\n  user: User | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n  login: (email: string, password: string) => Promise<void>;\r\n  logout: () => Promise<void>;\r\n  register: (userData: Partial<User>) => Promise<void>;\r\n  updateProfile: (userData: Partial<User>) => Promise<void>;\r\n  isAuthenticated: boolean;\r\n}\r\n\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\r\n\r\nexport const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    checkAuth();\r\n  }, []);\r\n\r\n  const checkAuth = async () => {\r\n    try {\r\n      const token = localStorage.getItem('token');\r\n      if (token) {\r\n        const response = await apiService.get<User>('/auth/me');\r\n        setUser(response.data);\r\n      }\r\n    } catch (error) {\r\n      localStorage.removeItem('token');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const login = async (email: string, password: string) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      const response = await apiService.post<{ token: string; user: User }>('/auth/login', {\r\n        email,\r\n        password,\r\n      });\r\n      localStorage.setItem('token', response.data.token);\r\n      setUser(response.data.user);\r\n      router.push('/dashboard');\r\n    } catch (error: any) {\r\n      setError(error.response?.data?.message || 'Login failed');\r\n      throw error;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const logout = async () => {\r\n    try {\r\n      setLoading(true);\r\n      await apiService.post('/auth/logout');\r\n      localStorage.removeItem('token');\r\n      setUser(null);\r\n      router.push('/auth/login');\r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const register = async (userData: Partial<User>) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      const response = await apiService.post<{ token: string; user: User }>('/auth/register', userData);\r\n      localStorage.setItem('token', response.data.token);\r\n      setUser(response.data.user);\r\n      router.push('/dashboard');\r\n    } catch (error: any) {\r\n      setError(error.response?.data?.message || 'Registration failed');\r\n      throw error;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const updateProfile = async (userData: Partial<User>) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      const response = await apiService.put<User>('/auth/profile', userData);\r\n      setUser(response.data);\r\n    } catch (error: any) {\r\n      setError(error.response?.data?.message || 'Profile update failed');\r\n      throw error;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const value = {\r\n    user,\r\n    loading,\r\n    error,\r\n    login,\r\n    logout,\r\n    register,\r\n    updateProfile,\r\n    isAuthenticated: !!user,\r\n  };\r\n\r\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\r\n};\r\n\r\nexport const useAuth = () => {\r\n  const context = useContext(AuthContext);\r\n  if (context === undefined) {\r\n    throw new Error('useAuth must be used within an AuthProvider');\r\n  }\r\n  return context;\r\n}; \r\ninterface User {\r\n  id: string;\r\n  name: string;\r\n  email: string;\r\n  avatar?: string;\r\n  role: string;\r\n  isAuthenticated: boolean;\r\n}\r\n\r\ninterface AuthContextType {\r\n  user: User | null;\r\n  isAuthenticated: boolean;\r\n  logout: () => void;\r\n}"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AACA;;;;;AAcA,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,eAAwD,CAAC,EAAE,QAAQ,EAAE;IAChF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,SAAS,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,OAAO;gBACT,MAAM,WAAW,MAAM,sHAAA,CAAA,aAAU,CAAC,GAAG,CAAO;gBAC5C,QAAQ,SAAS,IAAI;YACvB;QACF,EAAE,OAAO,OAAO;YACd,aAAa,UAAU,CAAC;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,WAAW;YACX,SAAS;YACT,MAAM,WAAW,MAAM,sHAAA,CAAA,aAAU,CAAC,IAAI,CAAgC,eAAe;gBACnF;gBACA;YACF;YACA,aAAa,OAAO,CAAC,SAAS,SAAS,IAAI,CAAC,KAAK;YACjD,QAAQ,SAAS,IAAI,CAAC,IAAI;YAC1B,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW;YAC1C,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,WAAW;YACX,MAAM,sHAAA,CAAA,aAAU,CAAC,IAAI,CAAC;YACtB,aAAa,UAAU,CAAC;YACxB,QAAQ;YACR,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,WAAW;YACX,SAAS;YACT,MAAM,WAAW,MAAM,sHAAA,CAAA,aAAU,CAAC,IAAI,CAAgC,kBAAkB;YACxF,aAAa,OAAO,CAAC,SAAS,SAAS,IAAI,CAAC,KAAK;YACjD,QAAQ,SAAS,IAAI,CAAC,IAAI;YAC1B,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW;YAC1C,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,WAAW;YACX,SAAS;YACT,MAAM,WAAW,MAAM,sHAAA,CAAA,aAAU,CAAC,GAAG,CAAO,iBAAiB;YAC7D,QAAQ,SAAS,IAAI;QACvB,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW;YAC1C,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA,iBAAiB,CAAC,CAAC;IACrB;IAEA,qBAAO,6WAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;AAEO,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,oUAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 484, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["import { useEffect, ReactNode } from 'react';\r\nimport { useRouter } from 'next/router';\r\nimport { useAuth } from '../../contexts/AuthContext';\r\n\r\ninterface ProtectedRouteProps {\r\n  children: ReactNode;\r\n  redirectTo?: string;\r\n}\r\n\r\nexport function ProtectedRoute({ children, redirectTo = '/auth/login' }: ProtectedRouteProps) {\r\n  const { user, isLoading } = useAuth();\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    if (!isLoading && !user) {\r\n      router.push(redirectTo);\r\n    }\r\n  }, [user, isLoading, router, redirectTo]);\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"min-h-screen flex items-center justify-center\">\r\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!user) {\r\n    return null;\r\n  }\r\n\r\n  return <>{children}</>;\r\n} "], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAOO,SAAS,eAAe,EAAE,QAAQ,EAAE,aAAa,aAAa,EAAuB;IAC1F,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAClC,MAAM,SAAS,CAAA,GAAA,6PAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,CAAC,MAAM;YACvB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAW;QAAQ;KAAW;IAExC,IAAI,WAAW;QACb,qBACE,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 535, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/app/dashboard/page.tsx"], "sourcesContent": ["// app/dashboard/page.tsx\nimport { MedicalEducationDashboard } from '@/components/dashboard/MedicalEducationDashboard';\nimport { ProtectedRoute } from '@/components/auth/ProtectedRoute';\n\nexport default function DashboardPage() {\n  return (\n    <ProtectedRoute>\n      <MedicalEducationDashboard />\n    </ProtectedRoute>\n  );\n}"], "names": [], "mappings": "AAAA,yBAAyB;;;;;AACzB;AACA;;;;AAEe,SAAS;IACtB,qBACE,6WAAC,4IAAA,CAAA,iBAAc;kBACb,cAAA,6WAAC,4JAAA,CAAA,4BAAyB;;;;;;;;;;AAGhC", "debugId": null}}]}