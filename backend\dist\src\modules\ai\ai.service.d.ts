import { Repository } from 'typeorm';
import { Material } from '../../entities/materials.entity';
import { LearningSuggestion } from '../../entities/learning-suggestion.entity';
import { RedisService } from '../redis/redis.service';
import { User } from '../../entities/user.entity';
interface Recommendation {
    materialId: string;
    score: number;
    reason: string;
}
export declare class AIService {
    private readonly userRepository;
    private readonly materialRepository;
    private readonly suggestionRepository;
    private readonly redisService;
    private readonly logger;
    private model;
    private readonly modelPath;
    private readonly cacheTTL;
    constructor(userRepository: Repository<User>, materialRepository: Repository<Material>, suggestionRepository: Repository<LearningSuggestion>, redisService: RedisService);
    private initializeModel;
    getRecommendations(userId: string): Promise<Recommendation[]>;
    private extractUserFeatures;
    private featuresToArray;
    private getRelevantMaterials;
    private calculateMaterialRelevance;
    private generateRecommendationReason;
    private calculateAverageScore;
    private calculateTotalStudyTime;
    private extractPreferredCategories;
    private getLastActivityDate;
    private calculateDifficultyPreference;
    private determineLearningStyle;
    private calculateEngagementLevel;
    private calculateQuizPerformance;
    private calculateMaterialInteraction;
    private calculateCategoryScore;
    private calculateActivityScore;
    trainModel(trainingData: {
        inputs: number[][];
        labels: number[];
    }): Promise<void>;
    private saveModel;
}
export {};
