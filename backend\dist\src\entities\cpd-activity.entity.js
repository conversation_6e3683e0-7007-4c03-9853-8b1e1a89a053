"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CPDActivity = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("./user.entity");
const cpd_tracking_entity_1 = require("./cpd-tracking.entity");
let CPDActivity = class CPDActivity {
};
exports.CPDActivity = CPDActivity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], CPDActivity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CPDActivity.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)('text'),
    __metadata("design:type", String)
], CPDActivity.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], CPDActivity.prototype, "points", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Date)
], CPDActivity.prototype, "activity_date", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], CPDActivity.prototype, "is_verified", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], CPDActivity.prototype, "verification_notes", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, user => user.cpd_activities),
    __metadata("design:type", user_entity_1.User)
], CPDActivity.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => cpd_tracking_entity_1.CPDCycle, cycle => cycle.activities),
    __metadata("design:type", cpd_tracking_entity_1.CPDCycle)
], CPDActivity.prototype, "cycle", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], CPDActivity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], CPDActivity.prototype, "updatedAt", void 0);
exports.CPDActivity = CPDActivity = __decorate([
    (0, typeorm_1.Entity)('cpd_activities')
], CPDActivity);
//# sourceMappingURL=cpd-activity.entity.js.map