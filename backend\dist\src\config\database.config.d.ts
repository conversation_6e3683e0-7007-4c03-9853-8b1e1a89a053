declare const _default: (() => {
    type: string;
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
    entities: string[];
    migrations: string[];
    migrationsTableName: string;
    synchronize: boolean;
}) & import("node_modules/@nestjs/config").ConfigFactoryKeyHost<{
    type: string;
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
    entities: string[];
    migrations: string[];
    migrationsTableName: string;
    synchronize: boolean;
}>;
export default _default;
