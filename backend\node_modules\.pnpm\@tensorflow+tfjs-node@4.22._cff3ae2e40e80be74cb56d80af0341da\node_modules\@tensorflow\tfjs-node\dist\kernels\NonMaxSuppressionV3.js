"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.nonMaxSuppressionV3Config = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var nodejs_kernel_backend_1 = require("../nodejs_kernel_backend");
exports.nonMaxSuppressionV3Config = {
    kernelName: tfjs_1.NonMaxSuppressionV3,
    backendName: 'tensorflow',
    kernelFunc: function (args) {
        var _a = args.inputs, boxes = _a.boxes, scores = _a.scores;
        var backend = args.backend;
        var _b = args.attrs, maxOutputSize = _b.maxOutputSize, iouThreshold = _b.iouThreshold, scoreThreshold = _b.scoreThreshold;
        var opAttrs = [(0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('T', boxes.dtype)];
        var maxOutputSizeTensor = (0, tfjs_1.scalar)(maxOutputSize, 'int32');
        var iouThresholdTensor = (0, tfjs_1.scalar)(iouThreshold);
        var scoreThresholdTensor = (0, tfjs_1.scalar)(scoreThreshold);
        var res = backend.executeSingleOutput(tfjs_1.NonMaxSuppressionV3, opAttrs, [
            boxes, scores, maxOutputSizeTensor, iouThresholdTensor,
            scoreThresholdTensor
        ]);
        maxOutputSizeTensor.dispose();
        iouThresholdTensor.dispose();
        scoreThresholdTensor.dispose();
        return res;
    }
};
