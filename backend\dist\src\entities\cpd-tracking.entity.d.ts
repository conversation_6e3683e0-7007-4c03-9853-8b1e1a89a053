import { User } from './user.entity';
export declare enum CPDActivityType {
    MATERIAL_COMPLETION = "material_completion",
    QUIZ_COMPLETION = "quiz_completion",
    CASE_STUDY = "case_study",
    GUIDELINE_REVIEW = "guideline_review",
    CLINICAL_UPDATE = "clinical_update"
}
export declare class CPDCycle {
    id: string;
    start_date: Date;
    end_date: Date;
    total_points: number;
    required_points: number;
    is_completed: boolean;
    user: User;
    activities: CPDActivity[];
    createdAt: Date;
    updatedAt: Date;
}
export declare class CPDActivity {
    id: string;
    title: string;
    description: string;
    points: number;
    activity_date: Date;
    is_verified: boolean;
    verification_notes: string;
    user: User;
    cycle: CPDCycle;
    createdAt: Date;
    updatedAt: Date;
}
