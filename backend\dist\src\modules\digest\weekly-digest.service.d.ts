import { Repository } from 'typeorm';
import { User } from '../../entities/user.entity';
import { Material } from '../../entities/materials.entity';
import { CPDActivity } from '../../entities/cpd-tracking.entity';
import { WeeklyDigest } from '../../entities/weekly-digest.entity';
export declare class WeeklyDigestService {
    private userRepository;
    private materialRepository;
    private cpdActivityRepository;
    private weeklyDigestRepository;
    constructor(userRepository: Repository<User>, materialRepository: Repository<Material>, cpdActivityRepository: Repository<CPDActivity>, weeklyDigestRepository: Repository<WeeklyDigest>);
    generateWeeklyDigest(userId: string): Promise<WeeklyDigest>;
    getLatestDigest(userId: string): Promise<WeeklyDigest | null>;
    markDigestAsRead(digestId: string): Promise<WeeklyDigest>;
    getUnreadDigestCount(userId: string): Promise<number>;
    private getRelevantMaterials;
    private calculatePoints;
}
