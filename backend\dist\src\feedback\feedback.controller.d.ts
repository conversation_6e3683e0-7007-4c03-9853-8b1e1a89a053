import { FeedbackService } from "./feedback.service";
export declare class FeedbackController {
    private readonly feedbackService;
    constructor(feedbackService: FeedbackService);
    create(body: {
        userId: string;
        comments: string;
        rating?: number;
        materialId?: string;
        unitId?: string;
    }): Promise<import("../entities").Feedback>;
    findAll(): Promise<import("../entities").Feedback[]>;
}
