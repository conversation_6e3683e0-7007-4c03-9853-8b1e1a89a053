#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1._578b962d7d194ae391c21d8451da687d/node_modules/ts-node/dist/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1._578b962d7d194ae391c21d8451da687d/node_modules/ts-node/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1._578b962d7d194ae391c21d8451da687d/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1._578b962d7d194ae391c21d8451da687d/node_modules/ts-node/dist/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1._578b962d7d194ae391c21d8451da687d/node_modules/ts-node/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1._578b962d7d194ae391c21d8451da687d/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../ts-node/dist/bin.js" "$@"
else
  exec node  "$basedir/../ts-node/dist/bin.js" "$@"
fi
