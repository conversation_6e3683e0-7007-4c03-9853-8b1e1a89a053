@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\medical\backend\node_modules\.pnpm\@nestjs+cli@11.0.6_@swc+cli_63762b8d6733f032d60e64f25107012b\node_modules\@nestjs\cli\bin\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\@nestjs+cli@11.0.6_@swc+cli_63762b8d6733f032d60e64f25107012b\node_modules\@nestjs\cli\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\@nestjs+cli@11.0.6_@swc+cli_63762b8d6733f032d60e64f25107012b\node_modules\@nestjs\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\@nestjs+cli@11.0.6_@swc+cli_63762b8d6733f032d60e64f25107012b\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\medical\backend\node_modules\.pnpm\@nestjs+cli@11.0.6_@swc+cli_63762b8d6733f032d60e64f25107012b\node_modules\@nestjs\cli\bin\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\@nestjs+cli@11.0.6_@swc+cli_63762b8d6733f032d60e64f25107012b\node_modules\@nestjs\cli\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\@nestjs+cli@11.0.6_@swc+cli_63762b8d6733f032d60e64f25107012b\node_modules\@nestjs\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\@nestjs+cli@11.0.6_@swc+cli_63762b8d6733f032d60e64f25107012b\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\@nestjs\cli\bin\nest.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\@nestjs\cli\bin\nest.js" %*
)
