import { Repository } from 'typeorm';
import { Flashcard } from '../entities/flashcard.entity';
import { QuizQuestion } from '../entities/quiz-question.entity';
export declare class FlashcardsService {
    private flashcardRepository;
    private quizQuestionRepository;
    constructor(flashcardRepository: Repository<Flashcard>, quizQuestionRepository: Repository<QuizQuestion>);
    createFlashcard(userId: string, questionId: string): Promise<Flashcard>;
    getDueCards(userId: string): Promise<Flashcard[]>;
    updateCard(cardId: string, quality: number): Promise<Flashcard>;
    getCardStats(userId: string): Promise<{
        total: number;
        due: number;
        upcoming: number;
        averageInterval: number;
    }>;
    syncCards(userId: string, cards: Partial<Flashcard>[]): Promise<Flashcard[]>;
}
