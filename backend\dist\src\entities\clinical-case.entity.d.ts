import { User } from './user.entity';
import { Unit } from './unit.entity';
export declare class ClinicalCase {
    id: number;
    title: string;
    patient_history: string;
    clinical_findings: string;
    lab_results: string;
    imaging_results: string;
    diagnosis: string;
    treatment: string;
    outcome_and_follow_up: string;
    learning_points: string;
    questions: any;
    is_published: boolean;
    view_count: number;
    author: User;
    related_units: Unit[];
    created_at: Date;
    updated_at: Date;
}
export declare class CreateClinicalCaseDto {
    title: string;
    patient_history: string;
    clinical_findings: string;
    lab_results?: string;
    imaging_results?: string;
    diagnosis: string;
    treatment: string;
    outcome_and_follow_up: string;
    learning_points: string;
    questions?: any;
    related_unit_ids: number[];
    is_published?: boolean;
}
