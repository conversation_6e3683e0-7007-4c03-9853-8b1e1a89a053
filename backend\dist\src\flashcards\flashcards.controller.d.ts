import { FlashcardsService } from './flashcards.service';
export declare class FlashcardsController {
    private readonly flashcardsService;
    constructor(flashcardsService: FlashcardsService);
    createFlashcard(userId: string, questionId: string): Promise<import("../entities/flashcard.entity").Flashcard>;
    getDueCards(userId: string): Promise<import("../entities/flashcard.entity").Flashcard[]>;
    updateCard(cardId: string, quality: number): Promise<import("../entities/flashcard.entity").Flashcard>;
    getCardStats(userId: string): Promise<{
        total: number;
        due: number;
        upcoming: number;
        averageInterval: number;
    }>;
    syncCards(userId: string, cards: any[]): Promise<import("../entities/flashcard.entity").Flashcard[]>;
}
