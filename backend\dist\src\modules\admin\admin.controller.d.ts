import { AdminService } from './admin.service';
import { User } from '../../entities/user.entity';
export declare class AdminController {
    private readonly adminService;
    constructor(adminService: AdminService);
    getAllUsers(): Promise<User[]>;
    getUserById(id: string): Promise<User | null>;
    createTestUser(userData: Partial<User>): Promise<User>;
    deleteUser(id: string): Promise<import("node_modules/typeorm").DeleteResult>;
    updateUser(id: string, userData: Partial<User>): Promise<User | null>;
}
