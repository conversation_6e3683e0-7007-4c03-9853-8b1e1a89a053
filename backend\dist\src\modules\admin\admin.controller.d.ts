import { AdminService } from './admin.service';
import { User } from '../../entities/user.entity';
export declare class AdminController {
    private readonly adminService;
    constructor(adminService: AdminService);
    getAllUsers(): Promise<any>;
    getUserById(id: string): Promise<any>;
    createTestUser(userData: Partial<User>): Promise<any>;
    deleteUser(id: string): Promise<any>;
    updateUser(id: string, userData: Partial<User>): Promise<any>;
}
