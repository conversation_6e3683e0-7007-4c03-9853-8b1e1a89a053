"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Unit = void 0;
const unit_quiz_entity_1 = require("../quiz/unit-quiz.entity");
const typeorm_1 = require("typeorm");
const typeorm_2 = require("typeorm");
let Unit = class Unit {
};
exports.Unit = Unit;
__decorate([
    (0, typeorm_2.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Unit.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => unit_quiz_entity_1.UnitQuiz, quiz => quiz.unit),
    __metadata("design:type", Array)
], Unit.prototype, "unitQuizzes", void 0);
exports.Unit = Unit = __decorate([
    (0, typeorm_2.Entity)('units')
], Unit);
//# sourceMappingURL=unit.entity.js.map