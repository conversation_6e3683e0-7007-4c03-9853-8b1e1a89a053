#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@tensorflow+tfjs@4.22.0_encoding@0.1.13_seedrandom@3.0.5/node_modules/@tensorflow/tfjs/dist/tools/custom_module/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@tensorflow+tfjs@4.22.0_encoding@0.1.13_seedrandom@3.0.5/node_modules/@tensorflow/tfjs/dist/tools/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@tensorflow+tfjs@4.22.0_encoding@0.1.13_seedrandom@3.0.5/node_modules/@tensorflow/tfjs/dist/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@tensorflow+tfjs@4.22.0_encoding@0.1.13_seedrandom@3.0.5/node_modules/@tensorflow/tfjs/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@tensorflow+tfjs@4.22.0_encoding@0.1.13_seedrandom@3.0.5/node_modules/@tensorflow/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@tensorflow+tfjs@4.22.0_encoding@0.1.13_seedrandom@3.0.5/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@tensorflow+tfjs@4.22.0_encoding@0.1.13_seedrandom@3.0.5/node_modules/@tensorflow/tfjs/dist/tools/custom_module/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@tensorflow+tfjs@4.22.0_encoding@0.1.13_seedrandom@3.0.5/node_modules/@tensorflow/tfjs/dist/tools/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@tensorflow+tfjs@4.22.0_encoding@0.1.13_seedrandom@3.0.5/node_modules/@tensorflow/tfjs/dist/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@tensorflow+tfjs@4.22.0_encoding@0.1.13_seedrandom@3.0.5/node_modules/@tensorflow/tfjs/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@tensorflow+tfjs@4.22.0_encoding@0.1.13_seedrandom@3.0.5/node_modules/@tensorflow/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@tensorflow+tfjs@4.22.0_encoding@0.1.13_seedrandom@3.0.5/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@tensorflow/tfjs/dist/tools/custom_module/cli.js" "$@"
else
  exec node  "$basedir/../@tensorflow/tfjs/dist/tools/custom_module/cli.js" "$@"
fi
