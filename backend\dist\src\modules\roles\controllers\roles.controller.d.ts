import { RoleInitializationService } from '../services/role-initialization.service';
import { Role } from '../../../entities/role.entity';
import { Permission } from '../../../entities/permission.entity';
import { Repository } from 'typeorm';
export declare class RolesController {
    private roleRepository;
    private permissionRepository;
    private roleInitializationService;
    constructor(roleRepository: Repository<Role>, permissionRepository: Repository<Permission>, roleInitializationService: RoleInitializationService);
    getAllRoles(): Promise<Role[]>;
    getRole(id: string): Promise<Role | null>;
    createRole(roleData: Partial<Role>): Promise<Role>;
    updateRole(id: string, roleData: Partial<Role>): Promise<Role | null>;
    deleteRole(id: string): Promise<{
        message: string;
    }>;
    initializeRoles(): Promise<{
        message: string;
    }>;
    getAllPermissions(): Promise<Permission[]>;
    createPermission(permissionData: Partial<Permission>): Promise<Permission>;
    updatePermission(id: string, permissionData: Partial<Permission>): Promise<Permission | null>;
    deletePermission(id: string): Promise<{
        message: string;
    }>;
}
