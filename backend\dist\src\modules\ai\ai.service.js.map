{"version": 3, "file": "ai.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/ai/ai.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,6CAAmD;AACnD,qCAAyC;AACzC,mCAAkC;AAClC,sEAA2D;AAC3D,0FAA+E;AAC/E,0DAAsD;AACtD,+BAA4B;AAC5B,4DAAkD;AAqC3C,IAAM,SAAS,iBAAf,MAAM,SAAS;IAMpB,YAEE,cAAiD,EAEjD,kBAAyD,EAEzD,oBAAqE,EACpD,YAA0B;QAL1B,mBAAc,GAAd,cAAc,CAAkB;QAEhC,uBAAkB,GAAlB,kBAAkB,CAAsB;QAExC,yBAAoB,GAApB,oBAAoB,CAAgC;QACpD,iBAAY,GAAZ,YAAY,CAAc;QAZ5B,WAAM,GAAG,IAAI,eAAM,CAAC,WAAS,CAAC,IAAI,CAAC,CAAC;QAGpC,aAAQ,GAAG,IAAI,CAAC;QAW/B,IAAI,CAAC,SAAS,GAAG,IAAA,WAAI,EAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,EAAE,sBAAsB,CAAC,CAAC;QACvE,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,IAAI,CAAC,KAAK,GAAG,IAAI,eAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;YACnE,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;QAChC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,MAAc;QACrC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,mBAAmB,MAAM,EAAE,CAAC;YAC7C,MAAM,qBAAqB,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACpE,IAAI,qBAAqB,EAAE,CAAC;gBAC1B,OAAO,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;YAC3C,CAAC;YAED,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAC5D,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC;YAC3E,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;YAEhE,MAAM,eAAe,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;gBACnD,UAAU,EAAE,QAAQ,CAAC,EAAE;gBACvB,KAAK,EAAE,KAAK,GAAG,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE,YAAY,CAAC;gBACtE,MAAM,EAAE,IAAI,CAAC,4BAA4B,CAAC,QAAQ,EAAE,YAAY,CAAC;aAClE,CAAC,CAAC,CAAC;YAEJ,MAAM,kBAAkB,GAAG,eAAe;iBACvC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;iBACjC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAEf,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CACzB,QAAQ,EACR,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAClC,IAAI,CAAC,QAAQ,CACd,CAAC;YAEF,OAAO,kBAAkB,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC9D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,MAAc;QAC9C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;YAC7C,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;YACrB,SAAS,EAAE,CAAC,iBAAiB,EAAE,aAAa,CAAC;SAC9C,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;QACpC,CAAC;QAED,MAAM,eAAe,GAAG,CAAC,IAAI,CAAC,eAAe,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAChE,GAAG,IAAI;YACP,SAAS,EAAE,IAAI,CAAC,SAAS,YAAY,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS;SAC1F,CAAC,CAA0B,CAAC;QAC7B,MAAM,WAAW,GAAG,IAAI,CAAC,WAA8B,IAAI,EAAE,aAAa,EAAE,GAAG,EAAE,CAAC;QAElF,OAAO;YACL,kBAAkB,EAAE,eAAe,CAAC,MAAM;YAC1C,YAAY,EAAE,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC;YACzD,SAAS,EAAE,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC;YACxD,mBAAmB,EAAE,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC;YACrE,YAAY,EAAE,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC;YACvD,oBAAoB,EAAE,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC;YACzE,aAAa,EAAE,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC;YACvD,eAAe,EAAE,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC;YAC/D,eAAe,EAAE,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC;YAC/D,mBAAmB,EAAE,IAAI,CAAC,4BAA4B,CAAC,eAAe,CAAC;SACxE,CAAC;IACJ,CAAC;IAEO,eAAe,CAAC,QAAsB;QAC5C,OAAO;YACL,QAAQ,CAAC,kBAAkB;YAC3B,QAAQ,CAAC,YAAY;YACrB,QAAQ,CAAC,SAAS;YAClB,QAAQ,CAAC,oBAAoB;YAC7B,QAAQ,CAAC,aAAa;YACtB,QAAQ,CAAC,eAAe;YACxB,QAAQ,CAAC,eAAe;YACxB,QAAQ,CAAC,mBAAmB;YAC5B,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,mBAAmB,CAAC;YACzD,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,YAAY,CAAC;SACnD,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,QAAsB;QACvD,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAClC,KAAK,EAAE;gBACL,QAAQ,EAAE,IAAA,YAAE,EAAC,QAAQ,CAAC,mBAAmB,CAAC;gBAC1C,UAAU,EAAE,QAAQ,CAAC,oBAAoB;aAC1C;YACD,SAAS,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;SAC9B,CAAC,CAAC;IACL,CAAC;IAEO,0BAA0B,CAAC,QAAkB,EAAE,QAAsB;QAC3E,OAAO,GAAG,CAAC;IACb,CAAC;IAEO,4BAA4B,CAAC,QAAkB,EAAE,QAAsB;QAC7E,OAAO,mDAAmD,QAAQ,CAAC,QAAQ,EAAE,CAAC;IAChF,CAAC;IAEO,qBAAqB,CAAC,OAA8B;QAC1D,IAAI,CAAC,OAAO,EAAE,MAAM;YAAE,OAAO,CAAC,CAAC;QAC/B,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;IACpF,CAAC;IAEO,uBAAuB,CAAC,OAA8B;QAC5D,IAAI,CAAC,OAAO,EAAE,MAAM;YAAE,OAAO,CAAC,CAAC;QAC/B,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtE,CAAC;IAEO,0BAA0B,CAAC,OAA8B;QAC/D,IAAI,CAAC,OAAO,EAAE,MAAM;YAAE,OAAO,EAAE,CAAC;QAChC,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxD,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;IAClC,CAAC;IAEO,mBAAmB,CAAC,OAA8B;QACxD,IAAI,CAAC,OAAO,EAAE,MAAM;YAAE,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;QACzC,OAAO,IAAI,IAAI,CACb,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CACvE,CAAC;IACJ,CAAC;IAEO,6BAA6B,CAAC,OAA8B;QAClE,IAAI,CAAC,OAAO,EAAE,MAAM;YAAE,OAAO,GAAG,CAAC;QACjC,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;IAC3F,CAAC;IAEO,sBAAsB,CAAC,WAA4B;QACzD,OAAO,WAAW,EAAE,aAAa,IAAI,GAAG,CAAC;IAC3C,CAAC;IAEO,wBAAwB,CAAC,OAA8B;QAC7D,IAAI,CAAC,OAAO,EAAE,MAAM;YAAE,OAAO,CAAC,CAAC;QAC/B,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;IACzF,CAAC;IAEO,wBAAwB,CAAC,OAA8B;QAC7D,IAAI,CAAC,OAAO,EAAE,MAAM;YAAE,OAAO,CAAC,CAAC;QAC/B,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;QACnE,OAAO,WAAW,CAAC,MAAM;YACvB,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,MAAM;YACpF,CAAC,CAAC,CAAC,CAAC;IACR,CAAC;IAEO,4BAA4B,CAAC,OAA8B;QACjE,IAAI,CAAC,OAAO,EAAE,MAAM;YAAE,OAAO,CAAC,CAAC;QAC/B,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;IAC/F,CAAC;IAEO,sBAAsB,CAAC,UAAoB;QACjD,OAAO,UAAU,CAAC,MAAM,GAAG,EAAE,CAAC;IAChC,CAAC;IAEO,sBAAsB,CAAC,YAAkB;QAC/C,MAAM,qBAAqB,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,YAAY,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAC5F,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,qBAAqB,GAAG,EAAE,CAAC,CAAC;IACrD,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,YAAsD;QACrE,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,MAAM,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;YACjE,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;YACvB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,SAAS;QACrB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AA9MY,8BAAS;oBAAT,SAAS;IADrB,IAAA,mBAAU,GAAE;IAQR,WAAA,IAAA,0BAAgB,EAAC,kBAAI,CAAC,CAAA;IAEtB,WAAA,IAAA,0BAAgB,EAAC,2BAAQ,CAAC,CAAA;IAE1B,WAAA,IAAA,0BAAgB,EAAC,+CAAkB,CAAC,CAAA;qCAHJ,oBAAU;QAEN,oBAAU;QAER,oBAAU;QAClB,4BAAY;GAblC,SAAS,CA8MrB"}