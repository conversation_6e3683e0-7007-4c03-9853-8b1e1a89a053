"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppDataSource = void 0;
const typeorm_1 = require("typeorm");
require("dotenv/config");
const path_1 = require("path");
exports.AppDataSource = new typeorm_1.DataSource({
    type: 'postgres', host: process.env.POSTGRES_HOST || 'localhost',
    port: parseInt(process.env.POSTGRES_PORT || '5432'),
    username: process.env.POSTGRES_USER || 'medical',
    password: process.env.POSTGRES_PASSWORD || 'postgres',
    database: process.env.POSTGRES_DB || 'medical_tracker',
    entities: [(0, path_1.join)(__dirname, '..', '**', '*.entity.{ts,js}')],
    migrations: [
        (0, path_1.join)(__dirname, '..', 'database', 'migrations', '*{.ts,.js}'),
        (0, path_1.join)(__dirname, '..', 'migrations', '*{.ts,.js}')
    ],
    migrationsTableName: 'migrations',
    synchronize: false,
    logging: process.env.NODE_ENV !== 'production',
});
exports.default = exports.AppDataSource;
//# sourceMappingURL=data-source.js.map