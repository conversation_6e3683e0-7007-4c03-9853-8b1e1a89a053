import { Cache } from 'cache-manager';
export declare class CacheService {
    private cacheManager;
    constructor(cacheManager: Cache);
    get<T>(key: string): Promise<T | undefined>;
    clear(prefix: string): Promise<void>;
    mget<T>(keys: string[]): Promise<(T | undefined)[]>;
    set<T>(key: string, value: T, ttl?: number): Promise<void>;
    delete(key: string): Promise<void>;
    reset(): Promise<void>;
    keys(pattern: string): Promise<string[]>;
    generateKey(prefix: string, params: Record<string, any>): string;
}
