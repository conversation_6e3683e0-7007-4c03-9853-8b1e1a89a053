"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.typeOrmConfig = exports.createTypeOrmConfig = void 0;
const typeorm_1 = require("typeorm");
const dotenv_1 = require("dotenv");
const path_1 = require("path");
(0, dotenv_1.config)();
const AppDataSource = new typeorm_1.DataSource({
    type: 'postgres',
    host: process.env.POSTGRES_HOST || 'localhost',
    port: parseInt(process.env.POSTGRES_PORT || '5432'),
    username: process.env.POSTGRES_USER || 'medical',
    password: process.env.POSTGRES_PASSWORD || 'AU110s/6081/2021MT',
    database: process.env.POSTGRES_DB || 'medical_tracker',
    entities: [(0, path_1.join)(__dirname, '..', '**', '*.entity.{ts,js}')],
    migrations: [(0, path_1.join)(__dirname, 'migrations', '*.{ts,js}')],
    migrationsTableName: 'migrations',
    synchronize: false,
    logging: process.env.NODE_ENV !== 'production',
    ssl: false
});
exports.default = AppDataSource;
const createTypeOrmConfig = (configService) => {
    return {
        type: 'postgres',
        host: configService.get('POSTGRES_HOST', 'localhost'),
        port: configService.get('POSTGRES_PORT', 5432),
        username: configService.get('POSTGRES_USER', 'medical'),
        password: configService.get('POSTGRES_PASSWORD', 'AU110s/6081/2021MT'),
        database: configService.get('POSTGRES_DB', 'medical_tracker'),
        entities: [(0, path_1.join)(__dirname, '..', '**', '*.entity.{ts,js}')],
        migrations: [(0, path_1.join)(__dirname, 'migrations', '*.{ts,js}')],
        synchronize: false,
        logging: configService.get('NODE_ENV') !== 'production',
    };
};
exports.createTypeOrmConfig = createTypeOrmConfig;
exports.typeOrmConfig = {
    type: 'postgres',
    host: process.env.DATABASE_HOST || 'localhost',
    port: parseInt(process.env.DATABASE_PORT || '5432'),
    username: process.env.DATABASE_USER || 'medical',
    password: process.env.DATABASE_PASSWORD || 'AU110s/6081/2021MT',
    database: process.env.DATABASE_NAME || 'medical_tracker',
    entities: [__dirname + '/../**/*.entity{.ts,.js}'],
    synchronize: true,
};
//# sourceMappingURL=typeorm.config.js.map