"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PasswordValidationPipe = void 0;
const common_1 = require("@nestjs/common");
let PasswordValidationPipe = class PasswordValidationPipe {
    transform(value) {
        if (!value.password) {
            return value;
        }
        const passwordRegex = /^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{10,})/;
        const passwordErrors = [];
        if (value.password.length < 10) {
            passwordErrors.push('Password must be at least 10 characters long');
        }
        if (!/[A-Z]/.test(value.password)) {
            passwordErrors.push('Password must contain at least one uppercase letter');
        }
        if (!/[a-z]/.test(value.password)) {
            passwordErrors.push('Password must contain at least one lowercase letter');
        }
        if (!/[0-9]/.test(value.password)) {
            passwordErrors.push('Password must contain at least one number');
        }
        if (!/[!@#$%^&*]/.test(value.password)) {
            passwordErrors.push('Password must contain at least one special character (!@#$%^&*)');
        }
        if (!passwordRegex.test(value.password)) {
            throw new common_1.BadRequestException({
                message: 'Password does not meet security requirements',
                details: passwordErrors
            });
        }
        return value;
    }
};
exports.PasswordValidationPipe = PasswordValidationPipe;
exports.PasswordValidationPipe = PasswordValidationPipe = __decorate([
    (0, common_1.Injectable)()
], PasswordValidationPipe);
//# sourceMappingURL=password-validation.pipe.js.map