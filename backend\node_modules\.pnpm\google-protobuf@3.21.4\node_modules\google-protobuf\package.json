{"name": "google-protobuf", "version": "3.21.4", "description": "Protocol Buffers for JavaScript", "main": "google-protobuf.js", "files": ["google/protobuf/*_pb.js", "google/protobuf/compiler/*_pb.js", "google-protobuf.js", "LICENSE.md", "LICENSE-asserts.md", "package.json", "README.md"], "dependencies": {}, "devDependencies": {"glob": "~7.1.4", "google-closure-compiler": "~20190819.0.0", "google-closure-deps": "^20210406.0.0", "google-closure-library": "~20200315.0.0", "gulp": "~5.0.0", "jasmine": "~3.5.0"}, "scripts": {"build": "node ./node_modules/gulp/bin/gulp.js dist", "test": "node ./node_modules/gulp/bin/gulp.js test", "clean": "node ./node_modules/gulp/bin/gulp.js clean"}, "repository": {"type": "git", "url": "https://github.com/protocolbuffers/protobuf-javascript"}, "author": "Google Protocol Buffers Team", "license": "(BSD-3-Clause AND Apache-2.0)"}