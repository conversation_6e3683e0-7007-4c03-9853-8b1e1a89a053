import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { Repository } from 'typeorm';
import { UnitQuizScore } from './unit-quiz-score.entity';
export declare class AnalyticsService {
    private readonly httpService;
    private readonly configService;
    private unitQuizScoreRepo;
    private readonly analyticsUrl;
    constructor(httpService: HttpService, configService: ConfigService, unitQuizScoreRepo: Repository<UnitQuizScore>);
    getLearningPatterns(userId: string): Promise<any>;
    getRecommendations(userId: string): Promise<any>;
    getPerformanceMetrics(userId: string): Promise<any>;
    getPeerBenchmarks(userId: string): Promise<any>;
    trackEvent(userId: string, eventType: string, data: any): Promise<void>;
    trackPageView(userId: string, page: string, metadata: any): Promise<void>;
    trackError(userId: string, error: Error, context: any): Promise<void>;
    trackPerformance(userId: string, metrics: any): Promise<void>;
    recordUnitQuizScore(userId: string, unitId: string, score: number): Promise<void>;
    getUnitCompletionRate(unitId: string): Promise<void>;
    unitLevelGapAnalysis(userId: string, unitId: string): Promise<void>;
}
