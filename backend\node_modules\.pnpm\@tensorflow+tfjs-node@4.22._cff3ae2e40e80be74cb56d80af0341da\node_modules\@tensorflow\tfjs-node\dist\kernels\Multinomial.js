"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.multinomialConfig = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var nodejs_kernel_backend_1 = require("../nodejs_kernel_backend");
exports.multinomialConfig = {
    kernelName: tfjs_1.Multinomial,
    backendName: 'tensorflow',
    kernelFunc: function (args) {
        var logits = args.inputs.logits;
        var backend = args.backend;
        var _a = args.attrs, numSamples = _a.numSamples, seed = _a.seed, normalized = _a.normalized;
        if (normalized) {
            throw new Error('TF Node backend does not support normalized logits ' +
                'passed to multinomial');
        }
        var opAttrs = [
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('T', logits.dtype),
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('output_dtype', 'int32'),
            { name: 'seed', type: backend.binding.TF_ATTR_INT, value: seed },
            { name: 'seed2', type: backend.binding.TF_ATTR_INT, value: seed * seed },
        ];
        var numSamplesTensor = (0, tfjs_1.scalar)(numSamples, 'int32');
        var res = backend.executeSingleOutput(tfjs_1.Multinomial, opAttrs, [logits, numSamplesTensor]);
        numSamplesTensor.dispose();
        return res;
    }
};
