{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,4NAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/components/ui/Button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n  isLoading?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, isLoading, children, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={isLoading || props.disabled}\n        {...props}\n      >\n        {isLoading ? (\n          <div className=\"mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent\" />\n        ) : null}\n        {children}\n      </Comp>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,uOACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,4RAAA,CAAA,aAAgB,MAC7B,QAA+E;QAA9E,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;IAC3E,MAAM,OAAO,UAAU,wSAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,4TAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,aAAa,MAAM,QAAQ;QACpC,GAAG,KAAK;;YAER,0BACC,4TAAC;gBAAI,WAAU;;;;;2DACb;YACH;;;;;;;AAGP;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/medical/frontend/src/app/error.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect } from 'react';\r\nimport { Button } from '@/components/ui/Button';\r\n\r\nexport default function Error({\r\n  error,\r\n  reset,\r\n}: {\r\n  error: Error & { digest?: string };\r\n  reset: () => void;\r\n}) {\r\n  useEffect(() => {\r\n    // Log the error to an error reporting service\r\n    console.error('Application error:', error);\r\n  }, [error]);\r\n\r\n  return (\r\n    <div className=\"flex min-h-screen flex-col items-center justify-center p-4\">\r\n      <div className=\"w-full max-w-md space-y-8 rounded-lg bg-white p-8 shadow-lg\">\r\n        <div className=\"text-center\">\r\n          <h1 className=\"text-2xl font-bold text-red-600\">Something went wrong!</h1>\r\n          <p className=\"mt-2 text-gray-600\">\r\n            {error.message || 'An unexpected error occurred'}\r\n          </p>\r\n        </div>\r\n        <div className=\"mt-6 flex justify-center space-x-4\">\r\n          <Button\r\n            onClick={() => reset()}\r\n            className=\"bg-blue-600 hover:bg-blue-700\"\r\n          >\r\n            Try again\r\n          </Button>\r\n          <Button\r\n            onClick={() => window.location.href = '/'}\r\n            variant=\"outline\"\r\n          >\r\n            Go to home\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS,MAAM,KAM7B;QAN6B,EAC5B,KAAK,EACL,KAAK,EAIN,GAN6B;;IAO5B,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;2BAAE;YACR,8CAA8C;YAC9C,QAAQ,KAAK,CAAC,sBAAsB;QACtC;0BAAG;QAAC;KAAM;IAEV,qBACE,4TAAC;QAAI,WAAU;kBACb,cAAA,4TAAC;YAAI,WAAU;;8BACb,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;4BAAG,WAAU;sCAAkC;;;;;;sCAChD,4TAAC;4BAAE,WAAU;sCACV,MAAM,OAAO,IAAI;;;;;;;;;;;;8BAGtB,4TAAC;oBAAI,WAAU;;sCACb,4TAAC,qIAAA,CAAA,SAAM;4BACL,SAAS,IAAM;4BACf,WAAU;sCACX;;;;;;sCAGD,4TAAC,qIAAA,CAAA,SAAM;4BACL,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;4BACtC,SAAQ;sCACT;;;;;;;;;;;;;;;;;;;;;;;AAOX;GAtCwB;KAAA", "debugId": null}}]}