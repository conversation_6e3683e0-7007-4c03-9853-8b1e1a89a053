"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIRecommendationService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_entity_1 = require("../../entities/user.entity");
const materials_entity_1 = require("../../entities/materials.entity");
const learning_suggestion_entity_1 = require("../../entities/learning-suggestion.entity");
const ai_service_1 = require("./ai.service");
let AIRecommendationService = class AIRecommendationService {
    constructor(userRepository, materialRepository, learningSuggestionRepository, aiService) {
        this.userRepository = userRepository;
        this.materialRepository = materialRepository;
        this.learningSuggestionRepository = learningSuggestionRepository;
        this.aiService = aiService;
    }
    async getPersonalizedRecommendations(userId) {
        const recommendations = await this.aiService.getRecommendations(userId);
        return recommendations.map((rec) => ({
            materialId: rec.materialId,
            score: rec.score,
            reason: rec.reason
        }));
    }
    async generateRecommendations(userId) {
        const user = await this.userRepository.findOne({ where: { id: userId } });
        if (!user)
            throw new Error('User not found');
        const userHistory = await this.getUserLearningHistory(userId);
        const userPreferences = await this.getUserPreferences(userId);
        const recommendations = await this.aiService.getRecommendations(userId);
        const recommendedMaterials = await this.materialRepository.find({
            where: {
                id: (0, typeorm_2.In)(recommendations.map((rec) => rec.materialId))
            },
            relations: ['author', 'unit']
        });
        return recommendedMaterials;
    }
    async getUserLearningHistory(userId) {
        return {};
    }
    async getUserPreferences(userId) {
        return {};
    }
};
exports.AIRecommendationService = AIRecommendationService;
exports.AIRecommendationService = AIRecommendationService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(materials_entity_1.Material)),
    __param(2, (0, typeorm_1.InjectRepository)(learning_suggestion_entity_1.LearningSuggestion)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        ai_service_1.AIService])
], AIRecommendationService);
//# sourceMappingURL=ai-recommendation.service.js.map