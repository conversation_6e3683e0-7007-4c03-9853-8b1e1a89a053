import { Material } from './materials.entity';
import { Progress } from './progress.entity';
import { Topic } from './topic.entity';
import { UnitQuiz } from './unit-quiz.entity';
export declare class Unit {
    id: string;
    title: string;
    description: string;
    order_index: number;
    topic: Topic;
    materials: Material[];
    progress: Progress[];
    unitQuizzes: UnitQuiz[];
    created_at: Date;
    updated_at: Date;
}
