import { User } from './user.entity';
import { Topic } from './topic.entity';
export declare class TopicProgress {
    id: string;
    user: User;
    topic: Topic;
    completion_percentage: number;
    time_spent_minutes: number;
    quiz_scores: {
        quiz_id: string;
        score: number;
        date: Date;
    }[];
    notes: {
        content: string;
        created_at: Date;
    }[];
    is_completed: boolean;
    completed_at: Date;
    streak_days: number;
    last_studied_at: Date;
    created_at: Date;
    updated_at: Date;
}
