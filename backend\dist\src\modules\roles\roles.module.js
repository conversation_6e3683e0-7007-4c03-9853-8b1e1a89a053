"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RolesModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const role_entity_1 = require("../../entities/role.entity");
const permission_entity_1 = require("../../entities/permission.entity");
const roles_controller_1 = require("./controllers/roles.controller");
const role_initialization_service_1 = require("./services/role-initialization.service");
let RolesModule = class RolesModule {
    constructor(roleInitializationService) {
        this.roleInitializationService = roleInitializationService;
    }
    async onModuleInit() {
        await this.roleInitializationService.initializeDefaultRoles();
    }
};
exports.RolesModule = RolesModule;
exports.RolesModule = RolesModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([role_entity_1.Role, permission_entity_1.Permission])],
        controllers: [roles_controller_1.RolesController],
        providers: [role_initialization_service_1.RoleInitializationService],
        exports: [role_initialization_service_1.RoleInitializationService],
    }),
    __metadata("design:paramtypes", [role_initialization_service_1.RoleInitializationService])
], RolesModule);
//# sourceMappingURL=roles.module.js.map