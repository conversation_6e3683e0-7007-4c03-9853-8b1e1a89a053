#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="C:\Users\<USER>\medical\backend\node_modules\.pnpm\@tensorflow+tfjs@4.22.0_encoding@0.1.13_seedrandom@3.0.5\node_modules\@tensorflow\tfjs\dist\tools\custom_module\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\@tensorflow+tfjs@4.22.0_encoding@0.1.13_seedrandom@3.0.5\node_modules\@tensorflow\tfjs\dist\tools\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\@tensorflow+tfjs@4.22.0_encoding@0.1.13_seedrandom@3.0.5\node_modules\@tensorflow\tfjs\dist\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\@tensorflow+tfjs@4.22.0_encoding@0.1.13_seedrandom@3.0.5\node_modules\@tensorflow\tfjs\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\@tensorflow+tfjs@4.22.0_encoding@0.1.13_seedrandom@3.0.5\node_modules\@tensorflow\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\@tensorflow+tfjs@4.22.0_encoding@0.1.13_seedrandom@3.0.5\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@tensorflow+tfjs@4.22.0_encoding@0.1.13_seedrandom@3.0.5/node_modules/@tensorflow/tfjs/dist/tools/custom_module/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@tensorflow+tfjs@4.22.0_encoding@0.1.13_seedrandom@3.0.5/node_modules/@tensorflow/tfjs/dist/tools/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@tensorflow+tfjs@4.22.0_encoding@0.1.13_seedrandom@3.0.5/node_modules/@tensorflow/tfjs/dist/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@tensorflow+tfjs@4.22.0_encoding@0.1.13_seedrandom@3.0.5/node_modules/@tensorflow/tfjs/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@tensorflow+tfjs@4.22.0_encoding@0.1.13_seedrandom@3.0.5/node_modules/@tensorflow/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/@tensorflow+tfjs@4.22.0_encoding@0.1.13_seedrandom@3.0.5/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../@tensorflow/tfjs/dist/tools/custom_module/cli.js" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../@tensorflow/tfjs/dist/tools/custom_module/cli.js" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../@tensorflow/tfjs/dist/tools/custom_module/cli.js" $args
  } else {
    & "node$exe"  "$basedir/../@tensorflow/tfjs/dist/tools/custom_module/cli.js" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
