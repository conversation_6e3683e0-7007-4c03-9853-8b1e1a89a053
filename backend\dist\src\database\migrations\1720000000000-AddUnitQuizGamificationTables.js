"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddUnitQuizGamificationTables1720000000000 = void 0;
const typeorm_1 = require("typeorm");
class AddUnitQuizGamificationTables1720000000000 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: "unit_quiz_scores",
            columns: [
                { name: "id", type: "uuid", isPrimary: true, isGenerated: true, generationStrategy: "uuid" },
                { name: "userId", type: "uuid" },
                { name: "unitId", type: "uuid" },
                { name: "score", type: "int" },
                { name: "passed", type: "boolean", default: false },
                { name: "createdAt", type: "timestamp", default: "now()" },
                { name: "updatedAt", type: "timestamp", default: "now()" }
            ],
            uniques: [{ columnNames: ["userId", "unitId"] }]
        }));
        await queryRunner.createForeignKey("unit_quiz_scores", new typeorm_1.TableForeignKey({
            columnNames: ["userId"],
            referencedColumnNames: ["id"],
            referencedTableName: "users",
            onDelete: "CASCADE"
        }));
        await queryRunner.createForeignKey("unit_quiz_scores", new typeorm_1.TableForeignKey({
            columnNames: ["unitId"],
            referencedColumnNames: ["id"],
            referencedTableName: "units",
            onDelete: "CASCADE"
        }));
        await queryRunner.createTable(new typeorm_1.Table({
            name: "xp_logs",
            columns: [
                { name: "id", type: "uuid", isPrimary: true, isGenerated: true, generationStrategy: "uuid" },
                { name: "userId", type: "uuid" },
                { name: "amount", type: "int" },
                { name: "reason", type: "varchar" },
                { name: "createdAt", type: "timestamp", default: "now()" }
            ]
        }));
        await queryRunner.createForeignKey("xp_logs", new typeorm_1.TableForeignKey({
            columnNames: ["userId"],
            referencedColumnNames: ["id"],
            referencedTableName: "users",
            onDelete: "CASCADE"
        }));
        await queryRunner.createTable(new typeorm_1.Table({
            name: "badges",
            columns: [
                { name: "id", type: "uuid", isPrimary: true, isGenerated: true, generationStrategy: "uuid" },
                { name: "userId", type: "uuid" },
                { name: "badgeType", type: "varchar" },
                { name: "awardedAt", type: "timestamp", default: "now()" }
            ]
        }));
        await queryRunner.createForeignKey("badges", new typeorm_1.TableForeignKey({
            columnNames: ["userId"],
            referencedColumnNames: ["id"],
            referencedTableName: "users",
            onDelete: "CASCADE"
        }));
        await queryRunner.createTable(new typeorm_1.Table({
            name: "streaks",
            columns: [
                { name: "id", type: "uuid", isPrimary: true, isGenerated: true, generationStrategy: "uuid" },
                { name: "userId", type: "uuid" },
                { name: "currentStreak", type: "int", default: 0 },
                { name: "lastUpdated", type: "timestamp", default: "now()" }
            ]
        }));
        await queryRunner.createForeignKey("streaks", new typeorm_1.TableForeignKey({
            columnNames: ["userId"],
            referencedColumnNames: ["id"],
            referencedTableName: "users",
            onDelete: "CASCADE"
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable("streaks");
        await queryRunner.dropTable("badges");
        await queryRunner.dropTable("xp_logs");
        await queryRunner.dropTable("unit_quiz_scores");
    }
}
exports.AddUnitQuizGamificationTables1720000000000 = AddUnitQuizGamificationTables1720000000000;
//# sourceMappingURL=1720000000000-AddUnitQuizGamificationTables.js.map