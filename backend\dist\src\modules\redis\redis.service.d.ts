import { ConfigService } from '@nestjs/config';
export declare class RedisService {
    private configService;
    private readonly logger;
    private readonly redis;
    private readonly memoryStore;
    private isConnected;
    constructor(configService: ConfigService);
    private getFromMemory;
    private setInMemory;
    get(key: string): Promise<string | null>;
    set(key: string, value: string, ttlSeconds?: number): Promise<void>;
    del(key: string): Promise<void>;
    exists(key: string): Promise<boolean>;
    onApplicationShutdown(): Promise<void>;
}
