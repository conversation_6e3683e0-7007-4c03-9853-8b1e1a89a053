{"version": 3, "file": "1710000000000-CreatePermissionsAndUpdateRoles.js", "sourceRoot": "", "sources": ["../../../src/migrations/1710000000000-CreatePermissionsAndUpdateRoles.ts"], "names": [], "mappings": ";;;AAAA,qCAAkF;AAElF,MAAa,4CAA4C;IAC9C,KAAK,CAAC,EAAE,CAAC,WAAwB;QAEpC,MAAM,WAAW,CAAC,WAAW,CACzB,IAAI,eAAK,CAAC;YACN,IAAI,EAAE,aAAa;YACnB,OAAO,EAAE;gBACL;oBACI,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,MAAM;oBACZ,SAAS,EAAE,IAAI;oBACf,kBAAkB,EAAE,MAAM;oBAC1B,OAAO,EAAE,oBAAoB;iBAChC;gBACD;oBACI,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,SAAS;oBACf,QAAQ,EAAE,IAAI;iBACjB;gBACD;oBACI,IAAI,EAAE,aAAa;oBACnB,IAAI,EAAE,SAAS;oBACf,UAAU,EAAE,IAAI;iBACnB;gBACD;oBACI,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,SAAS;oBACf,OAAO,EAAE,IAAI;iBAChB;gBACD;oBACI,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,SAAS;oBACf,UAAU,EAAE,IAAI;iBACnB;gBACD;oBACI,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,mBAAmB;iBAC/B;gBACD;oBACI,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,mBAAmB;iBAC/B;aACJ;SACJ,CAAC,EACF,IAAI,CACP,CAAC;QAGF,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;SAKvB,CAAC,CAAC;QAGH,MAAM,WAAW,CAAC,WAAW,CACzB,IAAI,eAAK,CAAC;YACN,IAAI,EAAE,kBAAkB;YACxB,OAAO,EAAE;gBACL;oBACI,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,MAAM;iBACf;gBACD;oBACI,IAAI,EAAE,eAAe;oBACrB,IAAI,EAAE,MAAM;iBACf;aACJ;SACJ,CAAC,EACF,IAAI,CACP,CAAC;QAGF,MAAM,WAAW,CAAC,gBAAgB,CAC9B,kBAAkB,EAClB,IAAI,yBAAe,CAAC;YAChB,WAAW,EAAE,CAAC,SAAS,CAAC;YACxB,qBAAqB,EAAE,CAAC,IAAI,CAAC;YAC7B,mBAAmB,EAAE,OAAO;YAC5B,QAAQ,EAAE,SAAS;SACtB,CAAC,CACL,CAAC;QAEF,MAAM,WAAW,CAAC,gBAAgB,CAC9B,kBAAkB,EAClB,IAAI,yBAAe,CAAC;YAChB,WAAW,EAAE,CAAC,eAAe,CAAC;YAC9B,qBAAqB,EAAE,CAAC,IAAI,CAAC;YAC7B,mBAAmB,EAAE,aAAa;YAClC,QAAQ,EAAE,SAAS;SACtB,CAAC,CACL,CAAC;QAGF,MAAM,WAAW,CAAC,KAAK,CAAC;;;SAGvB,CAAC,CAAC;IACP,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QAEtC,MAAM,oBAAoB,GAAG,MAAM,WAAW,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;QAC5E,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACxB,OAAO;QACX,CAAC;QAED,MAAM,cAAc,GAAG,oBAAoB,CAAC,WAAW,CAAC,IAAI,CACxD,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CACjD,CAAC;QACF,MAAM,oBAAoB,GAAG,oBAAoB,CAAC,WAAW,CAAC,IAAI,CAC9D,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CACvD,CAAC;QAEF,IAAI,cAAc,EAAE,CAAC;YACjB,MAAM,WAAW,CAAC,cAAc,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC;QACzE,CAAC;QACD,IAAI,oBAAoB,EAAE,CAAC;YACvB,MAAM,WAAW,CAAC,cAAc,CAAC,kBAAkB,EAAE,oBAAoB,CAAC,CAAC;QAC/E,CAAC;QAGD,MAAM,WAAW,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;QAGhD,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;SAKvB,CAAC,CAAC;QAGH,MAAM,WAAW,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;IAC/C,CAAC;CACJ;AA1ID,oGA0IC"}