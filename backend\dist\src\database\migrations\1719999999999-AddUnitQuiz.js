"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddUnitQuiz1719999999999 = void 0;
const typeorm_1 = require("typeorm");
class AddUnitQuiz1719999999999 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: "unit_quizzes",
            columns: [
                { name: "id", type: "uuid", isPrimary: true, isGenerated: true, generationStrategy: "uuid" },
                { name: "unitId", type: "uuid" },
                { name: "title", type: "varchar" },
                { name: "instructions", type: "text" },
                { name: "isPublished", type: "boolean", default: false },
                { name: "createdAt", type: "timestamp", default: "now()" },
                { name: "updatedAt", type: "timestamp", default: "now()" }
            ]
        }));
        await queryRunner.createForeignKey("unit_quizzes", new typeorm_1.TableForeignKey({
            columnNames: ["unitId"],
            referencedColumnNames: ["id"],
            referencedTableName: "units",
            onDelete: "CASCADE"
        }));
        await queryRunner.addColumn("quiz_questions", new typeorm_1.TableColumn({
            name: "unitQuizId",
            type: "uuid",
            isNullable: true
        }));
        await queryRunner.createForeignKey("quiz_questions", new typeorm_1.TableForeignKey({
            columnNames: ["unitQuizId"],
            referencedColumnNames: ["id"],
            referencedTableName: "unit_quizzes",
            onDelete: "CASCADE"
        }));
    }
    async down(queryRunner) {
        const table = await queryRunner.getTable("quiz_questions");
        if (table) {
            const fk = table.foreignKeys.find((fk) => fk.columnNames.indexOf("unitQuizId") !== -1);
            if (fk)
                await queryRunner.dropForeignKey("quiz_questions", fk);
        }
        await queryRunner.dropColumn("quiz_questions", "unitQuizId");
        await queryRunner.dropTable("unit_quizzes");
    }
}
exports.AddUnitQuiz1719999999999 = AddUnitQuiz1719999999999;
//# sourceMappingURL=1719999999999-AddUnitQuiz.js.map