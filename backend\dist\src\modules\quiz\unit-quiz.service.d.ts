import { Repository } from 'typeorm';
import { UnitQuiz } from '../../entities/unit-quiz.entity';
import { Unit } from '../../entities/unit.entity';
import { QuizQuestion } from '../../entities/quiz-question.entity';
export declare class UnitQuizService {
    private unitQuizRepo;
    private unitRepo;
    private questionRepo;
    constructor(unitQuizRepo: Repository<UnitQuiz>, unitRepo: Repository<Unit>, questionRepo: Repository<QuizQuestion>);
    generateQuiz(unitId: string): Promise<UnitQuiz>;
    validateAttempt(userId: string, unitId: string): Promise<boolean>;
    getQuizForUser(unitId: string): Promise<{
        title: string;
        instructions: string;
        questions: {
            id: string;
            text: string;
            options: string[];
        }[];
    }>;
    scoreQuiz(userId: string, unitId: string, answers: Record<string, string>): Promise<{
        score: number;
        passed: boolean;
        feedback: string;
    }>;
}
