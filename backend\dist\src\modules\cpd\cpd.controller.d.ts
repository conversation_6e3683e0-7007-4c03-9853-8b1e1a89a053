import { CPDService } from './cpd.service';
import { RequestWithUser } from '../../common/interfaces/request-with-user.interface';
export declare class CPDController {
    private readonly cpdService;
    constructor(cpdService: CPDService);
    verifyActivity(activityId: string, data: {
        verified: boolean;
        notes?: string;
    }): Promise<import("../../entities/cpd-tracking.entity").CPDActivity>;
    updateCPDCycle(req: RequestWithUser, data: {
        required_points?: number;
    }): Promise<import("../../entities/cpd-tracking.entity").CPDCycle>;
}
