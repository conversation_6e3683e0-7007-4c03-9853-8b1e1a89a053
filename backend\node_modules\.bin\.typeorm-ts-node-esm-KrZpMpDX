#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/typeorm@0.3.25_ioredis@5.6._5aa08973036f5c8815a1a194f5191181/node_modules/typeorm/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/typeorm@0.3.25_ioredis@5.6._5aa08973036f5c8815a1a194f5191181/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/typeorm@0.3.25_ioredis@5.6._5aa08973036f5c8815a1a194f5191181/node_modules/typeorm/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/typeorm@0.3.25_ioredis@5.6._5aa08973036f5c8815a1a194f5191181/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../typeorm/cli-ts-node-esm.js" "$@"
else
  exec node  "$basedir/../typeorm/cli-ts-node-esm.js" "$@"
fi
