@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\medical\backend\node_modules\.pnpm\typeorm@0.3.25_ioredis@5.6._5aa08973036f5c8815a1a194f5191181\node_modules\typeorm\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\typeorm@0.3.25_ioredis@5.6._5aa08973036f5c8815a1a194f5191181\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\medical\backend\node_modules\.pnpm\typeorm@0.3.25_ioredis@5.6._5aa08973036f5c8815a1a194f5191181\node_modules\typeorm\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\typeorm@0.3.25_ioredis@5.6._5aa08973036f5c8815a1a194f5191181\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\typeorm\cli-ts-node-esm.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\typeorm\cli-ts-node-esm.js" %*
)
