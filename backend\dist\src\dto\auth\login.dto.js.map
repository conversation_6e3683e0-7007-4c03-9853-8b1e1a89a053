{"version": 3, "file": "login.dto.js", "sourceRoot": "", "sources": ["../../../../src/dto/auth/login.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AACA,qDAA8G;AAC9G,6CAA8C;AAE9C,MAAa,QAAQ;CAoEpB;AApED,4BAoEC;AAhEC;IAHC,IAAA,4BAAU,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;IAC5B,IAAA,yBAAO,GAAE;IACT,IAAA,4BAAU,GAAE;;uCACE;AAKf;IAHC,IAAA,4BAAU,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IACzB,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;0CACK;AAKlB;IAHC,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,EAAC,CAAC,CAAC;;0CACI;AAIjB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,mBAAmB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAClE,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;0CACK;AAKlB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,iCAAiC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAChF,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;gDACW;AAKxB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,uBAAuB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACtE,IAAA,2BAAS,GAAE;IACX,IAAA,4BAAU,GAAE;;4CACQ;AAKrB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC5D,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;4CACO;AAIpB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,uCAAuC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACtF,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;4CACO;AAKpB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,yBAAyB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACxE,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;0CACK;AAKlB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,cAAc,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC7D,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;+CACU;AAKvB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,iBAAiB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAChE,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;sDACiB;AAK9B;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,gCAAgC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC/E,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;6CACQ;AAKrB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,2BAA2B,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC1E,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;2CACM;AAKnB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,mBAAmB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAClE,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;2CACM"}