"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddEmailVerification1716398784000 = void 0;
class AddEmailVerification1716398784000 {
    constructor() {
        this.name = 'AddEmailVerification1716398784000';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "users" ADD COLUMN "email_verification_token" varchar NULL`);
        await queryRunner.query(`ALTER TABLE "users" ADD COLUMN "email_verification_expires" timestamp NULL`);
        await queryRunner.query(`ALTER TABLE "users" ADD COLUMN "email_verified" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_users_email_verification_token" ON "users" ("email_verification_token") WHERE "email_verification_token" IS NOT NULL`);
    }
    async down(queryRunner) {
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_users_email_verification_token"`);
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "email_verified"`);
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "email_verification_expires"`);
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "email_verification_token"`);
    }
}
exports.AddEmailVerification1716398784000 = AddEmailVerification1716398784000;
//# sourceMappingURL=1716398784000-AddEmailVerification.js.map