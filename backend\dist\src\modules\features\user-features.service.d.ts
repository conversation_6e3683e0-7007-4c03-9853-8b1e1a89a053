import { RedisService } from '../redis/redis.service';
export interface UserActivity {
    userId: string;
    action: string;
    contentId?: string;
    contentType?: string;
    timestamp: Date;
    duration?: number;
    score?: number;
    metadata?: Record<string, any>;
}
export interface UserLearningPattern {
    preferredTimeSlots: string[];
    averageSessionDuration: number;
    learningVelocity: number;
    difficultyProgression: number;
    contentPreferences: Record<string, number>;
    weakAreas: string[];
    strongAreas: string[];
    engagementScore: number;
}
export interface UserFeatureProfile {
    userId: string;
    learningPattern: UserLearningPattern;
    behaviorMetrics: {
        totalSessions: number;
        averageScore: number;
        completionRate: number;
        retentionRate: number;
        streakDays: number;
        lastActiveDate: Date;
    };
    preferences: {
        contentTypes: Record<string, number>;
        difficultyLevel: string;
        learningStyle: string;
        topicInterests: Record<string, number>;
    };
    contextualFeatures: {
        timePatterns: Record<string, number>;
        deviceUsage: Record<string, number>;
        socialEngagement: number;
        helpSeekingBehavior: number;
    };
    lastUpdated: Date;
}
export declare class UserFeaturesService {
    private readonly redisService;
    private readonly logger;
    private readonly CACHE_TTL;
    private readonly FEATURE_CACHE_PREFIX;
    private readonly ACTIVITY_CACHE_PREFIX;
    constructor(redisService: RedisService);
    trackActivity(activity: UserActivity): Promise<void>;
    getUserFeatureProfile(userId: string): Promise<UserFeatureProfile | null>;
    getUserFeatureVector(userId: string): Promise<number[] | null>;
    private getRecentActivities;
    private updateUserFeaturesIncremental;
    private generateUserFeatureProfile;
    private extractLearningPattern;
    private extractBehaviorMetrics;
    private extractPreferences;
    private extractContextualFeatures;
    private getTimeSlot;
    private groupActivitiesBySessions;
    private calculateAverageSessionDuration;
    private calculateLearningVelocity;
    private calculateDifficultyProgression;
    private getContentPreferences;
    private identifyWeakAreas;
    private identifyStrongAreas;
    private calculateEngagementScore;
    private getPreferredTimeSlots;
    private calculateRetentionRate;
    private calculateStreakDays;
    private inferDifficultyPreference;
    private inferLearningStyle;
    private calculateSocialEngagement;
    private calculateHelpSeekingBehavior;
    private getDaysSinceLastActive;
    private getTopPreferenceScore;
    private getPreferenceDiversity;
    batchUpdateUserFeatures(userIds: string[]): Promise<void>;
    getFeatureStatistics(): Promise<Record<string, any>>;
}
