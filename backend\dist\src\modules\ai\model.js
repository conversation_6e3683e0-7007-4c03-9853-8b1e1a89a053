"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIModel = void 0;
const tf = __importStar(require("@tensorflow/tfjs-node"));
const path_1 = require("path");
const fs = __importStar(require("fs"));
class AIModel {
    constructor(modelPath) {
        this.modelPath = modelPath || (0, path_1.join)(process.cwd(), 'models', 'ai-model');
    }
    async initialize() {
        const model = tf.sequential();
        model.add(tf.layers.dense({
            units: 64,
            activation: 'relu',
            inputShape: [10],
        }));
        model.add(tf.layers.dropout({ rate: 0.2 }));
        model.add(tf.layers.dense({ units: 32, activation: 'relu' }));
        model.add(tf.layers.dropout({ rate: 0.2 }));
        model.add(tf.layers.dense({ units: 1, activation: 'sigmoid' }));
        model.compile({
            optimizer: 'adam',
            loss: 'binaryCrossentropy',
            metrics: ['accuracy'],
        });
        this.model = model;
    }
    async load() {
        try {
            const modelJsonPath = (0, path_1.join)(this.modelPath, 'model.json');
            if (fs.existsSync(modelJsonPath)) {
                const modelJson = fs.readFileSync(modelJsonPath, 'utf8');
                const modelData = JSON.parse(modelJson);
                throw new Error('Model loading not implemented yet');
            }
            else {
                throw new Error('Model file not found');
            }
        }
        catch (error) {
            throw new Error('Failed to load model');
        }
    }
    async save() {
        try {
            if (!fs.existsSync(this.modelPath)) {
                fs.mkdirSync(this.modelPath, { recursive: true });
            }
            console.log('Model save not implemented yet');
        }
        catch (error) {
            throw new Error('Failed to save model');
        }
    }
    async train(inputs, labels) {
        const inputTensor = tf.tensor2d(inputs);
        const labelTensor = tf.tensor2d(labels.map(label => [label]));
        try {
            await this.model.fit(inputTensor, labelTensor, {
                epochs: 10,
                batchSize: 32,
                validationSplit: 0.2,
            });
        }
        finally {
            inputTensor.dispose();
            labelTensor.dispose();
        }
    }
    async predict(input) {
        const inputTensor = tf.tensor2d([input]);
        try {
            const prediction = await this.model.predict(inputTensor);
            const value = (await prediction.data())[0];
            prediction.dispose();
            return value;
        }
        catch (error) {
            inputTensor.dispose();
            throw error;
        }
    }
}
exports.AIModel = AIModel;
//# sourceMappingURL=model.js.map