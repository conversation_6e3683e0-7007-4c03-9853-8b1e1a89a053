{"version": 3, "file": "security.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/auth/security.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAMwB;AACxB,6CAAmD;AACnD,qCAAqC;AACrC,2CAA+C;AAC/C,iDAAmC;AACnC,qDAAuC;AACvC,+CAAiC;AACjC,+CAAiC;AACjC,oEAAkG;AAClG,4DAAkD;AAElD,uEAAkE;AAW3D,IAAM,eAAe,uBAArB,MAAM,eAAe;IAG1B,YAEE,oBAA8D,EAE9D,WAA4C,EAE5C,iBAAoD,EAEpD,QAAkC,EAC1B,aAA4B,EAC5B,qBAA4C;QAR5C,yBAAoB,GAApB,oBAAoB,CAAkC;QAEtD,gBAAW,GAAX,WAAW,CAAyB;QAEpC,sBAAiB,GAAjB,iBAAiB,CAA2B;QAE5C,aAAQ,GAAR,QAAQ,CAAkB;QAC1B,kBAAa,GAAb,aAAa,CAAe;QAC5B,0BAAqB,GAArB,qBAAqB,CAAuB;QAZrC,WAAM,GAAG,IAAI,eAAM,CAAC,iBAAe,CAAC,IAAI,CAAC,CAAC;IAaxD,CAAC;IAEI,mBAAmB,CAAC,KAAK,GAAG,EAAE;QACpC,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;QACrE,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,MAAc;QACjC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;QACpE,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,8BAAqB,CAAC,gBAAgB,CAAC,CAAC;QACpD,CAAC;QAED,MAAM,MAAM,GAAG,SAAS,CAAC,cAAc,CAAC;YACtC,IAAI,EAAE,cAAc,IAAI,CAAC,KAAK,EAAE;YAChC,MAAM,EAAE,YAAY;SACrB,CAAC,CAAC;QAEH,IAAI,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;YACrD,KAAK,EAAE,EAAE,MAAM,EAAE;SAClB,CAAC,CAAC;QACH,MAAM,YAAY,GAAG;YACnB,MAAM;YACN,eAAe,EAAE,MAAM,CAAC,MAAM;YAC9B,WAAW,EAAE,IAAI,CAAC,mBAAmB,EAAE;SACxC,CAAC;QAEF,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YACnE,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC/D,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;YACtC,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC;YACtC,MAAM,EAAE,MAAM,CAAC,KAAK;YACpB,KAAK,EAAE,cAAc,IAAI,CAAC,KAAK,EAAE;YACjC,MAAM,EAAE,YAAY;YACpB,QAAQ,EAAE,QAAQ;SACnB,CAAC,CAAC;QACH,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAEtD,OAAO;YACL,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,MAAM,EAAE,UAAU;SACnB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,MAAc,EAAE,KAAa;QACjD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;YACvD,KAAK,EAAE,EAAE,MAAM,EAAE;SAClB,CAAC,CAAC;QACH,IAAI,CAAC,QAAQ,EAAE,eAAe,EAAE,CAAC;YAC/B,MAAM,IAAI,4BAAmB,CAAC,gBAAgB,CAAC,CAAC;QAClD,CAAC;QAED,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC;YAC3B,MAAM,EAAE,QAAQ,CAAC,eAAe;YAChC,QAAQ,EAAE,QAAQ;YAClB,KAAK;SACN,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,MAAc,EAAE,GAAsB;QACzD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;QACpE,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,8BAAqB,CAAC,gBAAgB,CAAC,CAAC;QACpD,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC9E,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,8BAAqB,CAAC,+BAA+B,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,GAAG,CAAC,WAAW,KAAK,GAAG,CAAC,eAAe,EAAE,CAAC;YAC5C,MAAM,IAAI,4BAAmB,CAAC,4BAA4B,CAAC,CAAC;QAC9D,CAAC;QAED,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QAC5D,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAG/B,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,iBAAiB,EAAE;YACrD,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,MAAc,EAAE,WAAgC;QAC3E,IAAI,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;YACrD,KAAK,EAAE,EAAE,MAAM,EAAE;SAClB,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;gBACnD,MAAM;gBACN,GAAG,WAAW;aACf,CAAC,CAAC;YACH,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC/D,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;YACrC,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,0BAA0B,EAAE;YAC9D,OAAO,EAAE,WAAW;YACpB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,MAAc;QACpC,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;YAC3B,KAAK,EAAE;gBACL,MAAM;gBACN,QAAQ,EAAE,IAAI;aACf;YACD,KAAK,EAAE;gBACL,YAAY,EAAE,MAAM;aACrB;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,MAAc,EAAE,SAAiB;QACnD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;YAC7C,KAAK,EAAE;gBACL,EAAE,EAAE,SAAS;gBACb,MAAM;aACP;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,4BAAmB,CAAC,mBAAmB,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;QACzB,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAGrC,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YAClB,MAAM,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QACnF,CAAC;QAGD,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,iBAAiB,EAAE;YACrD,SAAS;YACT,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,SAAiB,EAAE,SAA4B;QACpF,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;YAC1C,MAAM;YACN,SAAS;YACT,SAAS;YACT,SAAS,EAAE,SAAS,CAAC,SAAS,IAAI,SAAS;YAC3C,QAAQ,EAAE,SAAS,CAAC,QAAQ;YAC5B,SAAS,EAAE,SAAS,CAAC,SAAS;SAC/B,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,MAAc,EAAE,KAAK,GAAG,EAAE;QAChD,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;YACjC,KAAK,EAAE,EAAE,MAAM,EAAE;YACjB,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;YAC5B,IAAI,EAAE,KAAK;SACZ,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,yBAAyB;QACrC,OAAO,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,IAAU;QACpC,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjE,MAAM,mBAAmB,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAEvE,IAAI,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;YACrD,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;SAC3B,CAAC,CAAC;QACH,MAAM,gBAAgB,GAAG;YACvB,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,sBAAsB,EAAE,iBAAiB;YACzC,wBAAwB,EAAE,mBAAmB;YAC7C,eAAe,EAAE,KAAK;SACvB,CAAC;QAEF,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;YACvE,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC/D,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;YAC1C,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC3C,OAAO,CAAC,GAAG,CACT,yBAAyB,IAAI,CAAC,KAAK,mDAAmD,iBAAiB,EAAE,CAC1G,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,KAAa;QAC7B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;YACvD,KAAK,EAAE,EAAE,sBAAsB,EAAE,KAAK,EAAE;SACzC,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,8BAAqB,CAAC,4BAA4B,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,wBAAwB,IAAI,QAAQ,CAAC,wBAAwB,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC;YACzF,MAAM,IAAI,8BAAqB,CAAC,gCAAgC,CAAC,CAAC;QACpE,CAAC;QAGD,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC;QAChC,QAAQ,CAAC,sBAAsB,GAAG,IAAI,CAAC;QACvC,QAAQ,CAAC,wBAAwB,GAAG,IAAI,CAAC;QACzC,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE/C,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,MAAc;QAC1C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;QACpE,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,8BAAqB,CAAC,gBAAgB,CAAC,CAAC;QACpD,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;YACvD,KAAK,EAAE,EAAE,MAAM,EAAE;SAClB,CAAC,CAAC;QACH,IAAI,QAAQ,EAAE,eAAe,EAAE,CAAC;YAC9B,MAAM,IAAI,4BAAmB,CAAC,2BAA2B,CAAC,CAAC;QAC7D,CAAC;QAED,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,MAAc;QAClC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;YACvD,KAAK,EAAE,EAAE,MAAM,EAAE;SAClB,CAAC,CAAC;QACH,OAAO,QAAQ,EAAE,eAAe,IAAI,KAAK,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,MAAc;QACtC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;YACvD,KAAK,EAAE,EAAE,MAAM,EAAE;SAClB,CAAC,CAAC;QACH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,0BAAiB,CAAC,6BAA6B,CAAC,CAAC;QAC7D,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,MAAc;QACzC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;YACvD,KAAK,EAAE,EAAE,MAAM,EAAE;SAClB,CAAC,CAAC;QACH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,0BAAiB,CAAC,6BAA6B,CAAC,CAAC;QAC7D,CAAC;QACD,MAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC5C,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC;QAChC,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,KAAa;QAEzC,OAAO,CAAC,GAAG,CAAC,mCAAmC,KAAK,EAAE,CAAC,CAAC;IAC1D,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,KAAa,EAAE,OAAkC;QAE3E,OAAO,CAAC,GAAG,CAAC,yCAAyC,KAAK,gBAAgB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACrG,OAAO,IAAI,CAAC;IACd,CAAC;CACF,CAAA;AA5SY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,sCAAoB,CAAC,CAAA;IAEtC,WAAA,IAAA,0BAAgB,EAAC,6BAAW,CAAC,CAAA;IAE7B,WAAA,IAAA,0BAAgB,EAAC,+BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,kBAAI,CAAC,CAAA;yDALO,oBAAU,oBAAV,oBAAU,oDAEnB,oBAAU,oBAAV,oBAAU,oDAEJ,oBAAU,oBAAV,oBAAU,oDAEnB,oBAAU,oBAAV,oBAAU,gCACL,sBAAa;QACL,+CAAqB;GAb3C,eAAe,CA4S3B"}