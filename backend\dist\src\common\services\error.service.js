"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var ErrorService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorService = void 0;
const common_1 = require("@nestjs/common");
let ErrorService = ErrorService_1 = class ErrorService {
    constructor() {
        this.logger = new common_1.Logger(ErrorService_1.name);
        this.isDevelopment = process.env.NODE_ENV === 'development';
    }
    logError(error, context, sensitiveData) {
        if (this.isDevelopment) {
            this.logger.error({
                message: error.message,
                stack: error.stack,
                context,
                sensitiveData,
            });
            return;
        }
        this.logger.error({
            message: this.sanitizeErrorMessage(error.message),
            context,
            timestamp: new Date().toISOString(),
        });
    }
    sanitizeErrorMessage(message) {
        if (message.includes('password')) {
            return 'Authentication error occurred';
        }
        if (message.includes('email') || message.includes('username')) {
            return 'Invalid credentials';
        }
        if (message.includes('token')) {
            return 'Session error occurred';
        }
        if (message.includes('permission') || message.includes('forbidden')) {
            return 'Access denied';
        }
        return 'An error occurred';
    }
    getPublicErrorMessage(error) {
        return this.sanitizeErrorMessage(error.message);
    }
};
exports.ErrorService = ErrorService;
exports.ErrorService = ErrorService = ErrorService_1 = __decorate([
    (0, common_1.Injectable)()
], ErrorService);
//# sourceMappingURL=error.service.js.map