"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeeklyDigestController = void 0;
const common_1 = require("@nestjs/common");
const weekly_digest_service_1 = require("./weekly-digest.service");
const jwt_auth_guard_1 = require("../auth/jwt-auth.guard");
const roles_guards_1 = require("../../common/guards/roles.guards");
let WeeklyDigestController = class WeeklyDigestController {
    constructor(weeklyDigestService) {
        this.weeklyDigestService = weeklyDigestService;
    }
};
exports.WeeklyDigestController = WeeklyDigestController;
exports.WeeklyDigestController = WeeklyDigestController = __decorate([
    (0, common_1.Controller)('weekly-digest'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guards_1.RolesGuard),
    __metadata("design:paramtypes", [weekly_digest_service_1.WeeklyDigestService])
], WeeklyDigestController);
//# sourceMappingURL=weekly-digest.controller.js.map