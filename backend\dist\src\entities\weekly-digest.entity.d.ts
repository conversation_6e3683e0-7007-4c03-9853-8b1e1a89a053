import { User } from './user.entity';
import { Material } from './materials.entity';
export declare class WeeklyDigest {
    id: string;
    user: User;
    week_start_date: Date;
    week_end_date: Date;
    is_sent: boolean;
    sent_at: Date;
    status: 'pending' | 'read' | 'archived';
    read_at: Date;
    content: {
        new_materials: {
            material: Material;
            relevance: number;
        }[];
        recommended_topics: {
            topic: string;
            reason: string;
            materials: Material[];
        }[];
        cpd_progress: {
            points_earned: number;
            points_required: number;
            activities: {
                type: string;
                points: number;
                date: Date;
            }[];
        };
        upcoming_deadlines: {
            type: string;
            description: string;
            due_date: Date;
        }[];
    };
    delivery_status: {
        email?: {
            sent: boolean;
            error?: string;
            timestamp?: Date;
        };
        push?: {
            sent: boolean;
            error?: string;
            timestamp?: Date;
        };
    };
    created_at: Date;
    updated_at: Date;
}
