import { User } from './user.entity';
import { Quiz } from './quiz.entity';
export declare class QuizAttempt {
    id: string;
    user: User;
    quiz: Quiz;
    score: number;
    responses: {
        questionId: string;
        isCorrect: boolean;
        responseTime: number;
    }[];
    metadata: {
        duration: number;
        startTime: Date;
        endTime: Date;
        deviceInfo: any;
    };
    synced: boolean;
    created_at: Date;
    updated_at: Date;
}
