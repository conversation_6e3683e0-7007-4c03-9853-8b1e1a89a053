@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\medical\backend\node_modules\.pnpm\ts-jest@29.3.1_@babel+core@_1bcdc6a2110a62890ad4e7ab70a0d901\node_modules\ts-jest\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\ts-jest@29.3.1_@babel+core@_1bcdc6a2110a62890ad4e7ab70a0d901\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\medical\backend\node_modules\.pnpm\ts-jest@29.3.1_@babel+core@_1bcdc6a2110a62890ad4e7ab70a0d901\node_modules\ts-jest\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\ts-jest@29.3.1_@babel+core@_1bcdc6a2110a62890ad4e7ab70a0d901\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\ts-jest\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\ts-jest\cli.js" %*
)
