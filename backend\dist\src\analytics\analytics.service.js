"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const progress_entity_1 = require("../entities/progress.entity");
const user_response_entity_1 = require("../entities/user-response.entity");
const study_event_entity_1 = require("../entities/study-event.entity");
const flashcard_entity_1 = require("../entities/flashcard.entity");
const quiz_attempt_entity_1 = require("../entities/quiz-attempt.entity");
const tf = __importStar(require("@tensorflow/tfjs-node"));
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
let AnalyticsService = class AnalyticsService {
    constructor(progressRepository, userResponseRepository, studyEventRepository, flashcardRepository, quizAttemptRepository) {
        this.progressRepository = progressRepository;
        this.userResponseRepository = userResponseRepository;
        this.studyEventRepository = studyEventRepository;
        this.flashcardRepository = flashcardRepository;
        this.quizAttemptRepository = quizAttemptRepository;
        this.modelPath = path.join(process.cwd(), 'models', 'performance-prediction');
        this.initializeModel();
    }
    async initializeModel() {
        try {
            if (!fs.existsSync(this.modelPath)) {
                fs.mkdirSync(this.modelPath, { recursive: true });
            }
            const modelJsonPath = path.join(this.modelPath, 'model.json');
            if (fs.existsSync(modelJsonPath)) {
                const modelJson = fs.readFileSync(modelJsonPath, 'utf8');
                const modelData = JSON.parse(modelJson);
                this.predictionModel = this.createPredictionModel();
            }
            else {
                this.predictionModel = this.createPredictionModel();
                console.log('Model created successfully');
            }
        }
        catch (error) {
            console.error('Error initializing model:', error);
            this.predictionModel = this.createPredictionModel();
        }
    }
    createPredictionModel() {
        const model = tf.sequential();
        model.add(tf.layers.dense({ units: 64, activation: 'relu', inputShape: [10] }));
        model.add(tf.layers.dropout({ rate: 0.2 }));
        model.add(tf.layers.dense({ units: 32, activation: 'relu' }));
        model.add(tf.layers.dense({ units: 1, activation: 'sigmoid' }));
        model.compile({ optimizer: 'adam', loss: 'meanSquaredError' });
        return model;
    }
    async trackEvent(eventData) {
        const event = this.studyEventRepository.create({
            user_id: eventData.user_id,
            event_type: eventData.event_type,
            data: eventData.data,
            timestamp: new Date(eventData.timestamp),
        });
        return this.studyEventRepository.save(event);
    }
    async batchTrackEvents(events) {
        const studyEvents = events.map(event => this.studyEventRepository.create({
            user_id: event.user_id,
            event_type: event.event_type,
            data: event.data,
            timestamp: new Date(event.timestamp),
        }));
        return this.studyEventRepository.save(studyEvents);
    }
    async calculatePerformanceMetrics(user_id) {
        const [flashcards, quizAttempts] = await Promise.all([
            this.flashcardRepository.find({ where: { user: { id: user_id } } }),
            this.quizAttemptRepository.find({ where: { user: { id: user_id } } }),
        ]);
        const now = new Date();
        const metrics = {
            totalCards: flashcards.length,
            cardsDue: flashcards.filter((card) => new Date(card.next_review) <= now).length,
            averageInterval: this.calculateAverageInterval(flashcards),
            successRate: this.calculateSuccessRate(flashcards),
            quizPerformance: this.calculateQuizPerformance(quizAttempts),
            studyStreak: await this.calculateStudyStreak(user_id),
        };
        return metrics;
    }
    async generatePredictions(user_id) {
        const features = await this.extractFeatures(user_id);
        const prediction = await this.predictionModel.predict(tf.tensor2d([features]));
        const predictionValue = await prediction.data();
        return {
            predictedSuccessRate: predictionValue[0],
            confidence: this.calculateConfidence(features),
            recommendedStudyTime: this.calculateRecommendedStudyTime(features),
        };
    }
    async analyzeStudyPatterns(user_id) {
        const events = await this.studyEventRepository.find({
            where: { user_id },
            order: { timestamp: 'ASC' },
        });
        return {
            preferredStudyTimes: this.analyzePreferredStudyTimes(events),
            studyDuration: this.analyzeStudyDuration(events),
            performanceByTopic: await this.analyzePerformanceByTopic(user_id),
            consistencyScore: this.calculateConsistencyScore(events),
        };
    }
    async generateRecommendations(user_id) {
        const [metrics, patterns, predictions] = await Promise.all([
            this.calculatePerformanceMetrics(user_id),
            this.analyzeStudyPatterns(user_id),
            this.generatePredictions(user_id),
        ]);
        return {
            studySchedule: this.generateStudySchedule(metrics, patterns),
            focusAreas: this.identifyFocusAreas(metrics),
            improvementTips: this.generateImprovementTips(metrics, patterns, predictions),
        };
    }
    calculateAverageInterval(flashcards) {
        if (flashcards.length === 0)
            return 0;
        return flashcards.reduce((sum, card) => sum + card.interval, 0) / flashcards.length;
    }
    calculateSuccessRate(flashcards) {
        if (flashcards.length === 0)
            return 0;
        return flashcards.reduce((sum, card) => sum + (card.correct_streak > 0 ? 1 : 0), 0) / flashcards.length;
    }
    calculateQuizPerformance(attempts) {
        if (attempts.length === 0)
            return { averageScore: 0, totalAttempts: 0 };
        const averageScore = attempts.reduce((sum, attempt) => sum + attempt.score, 0) / attempts.length;
        return {
            averageScore,
            totalAttempts: attempts.length,
            improvement: this.calculateImprovement(attempts),
        };
    }
    async calculateStudyStreak(user_id) {
        const events = await this.studyEventRepository.find({
            where: { user_id },
            order: { timestamp: 'DESC' },
        });
        let streak = 0;
        let currentDate = new Date();
        currentDate.setHours(0, 0, 0, 0);
        for (const event of events) {
            const eventDate = new Date(event.timestamp);
            eventDate.setHours(0, 0, 0, 0);
            if (eventDate.getTime() === currentDate.getTime()) {
                continue;
            }
            if (eventDate.getTime() === currentDate.getTime() - 86400000) {
                streak++;
                currentDate = eventDate;
            }
            else {
                break;
            }
        }
        return streak;
    }
    async extractFeatures(user_id) {
        const [metrics, patterns] = await Promise.all([
            this.calculatePerformanceMetrics(user_id),
            this.analyzeStudyPatterns(user_id),
        ]);
        return [
            metrics.successRate,
            metrics.averageInterval,
            patterns.consistencyScore,
            metrics.quizPerformance.averageScore,
            metrics.studyStreak,
        ];
    }
    calculateConfidence(features) {
        return 0.8;
    }
    calculateRecommendedStudyTime(features) {
        return 60;
    }
    analyzePreferredStudyTimes(events) {
        return {
            morning: 0.3,
            afternoon: 0.4,
            evening: 0.3,
        };
    }
    analyzeStudyDuration(events) {
        return {
            averageDuration: 45,
            longestSession: 120,
            shortestSession: 15,
        };
    }
    async analyzePerformanceByTopic(user_id) {
        return {
            anatomy: 0.85,
            physiology: 0.75,
            pathology: 0.65,
        };
    }
    calculateConsistencyScore(events) {
        return 0.7;
    }
    generateStudySchedule(metrics, patterns) {
        return {
            recommendedTimes: ['09:00', '15:00', '20:00'],
            duration: 45,
            frequency: 'daily',
        };
    }
    identifyFocusAreas(metrics) {
        return ['Topic A', 'Topic B'];
    }
    generateImprovementTips(metrics, patterns, predictions) {
        return [
            'Focus on reviewing difficult topics',
            'Increase study consistency',
            'Take more practice quizzes',
        ];
    }
    calculateImprovement(attempts) {
        if (attempts.length < 2)
            return 0;
        const recentAttempts = attempts.slice(-5);
        const improvement = recentAttempts[recentAttempts.length - 1].score - recentAttempts[0].score;
        return improvement;
    }
};
exports.AnalyticsService = AnalyticsService;
exports.AnalyticsService = AnalyticsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(progress_entity_1.Progress)),
    __param(1, (0, typeorm_1.InjectRepository)(user_response_entity_1.UserResponse)),
    __param(2, (0, typeorm_1.InjectRepository)(study_event_entity_1.StudyEvent)),
    __param(3, (0, typeorm_1.InjectRepository)(flashcard_entity_1.Flashcard)),
    __param(4, (0, typeorm_1.InjectRepository)(quiz_attempt_entity_1.QuizAttempt)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], AnalyticsService);
//# sourceMappingURL=analytics.service.js.map