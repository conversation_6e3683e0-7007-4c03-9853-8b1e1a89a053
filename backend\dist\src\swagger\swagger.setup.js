"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupSwagger = setupSwagger;
const swagger_1 = require("@nestjs/swagger");
function setupSwagger(app) {
    const config = new swagger_1.DocumentBuilder()
        .setTitle('MedTrack Hub API')
        .setDescription('API documentation for the MedTrack Hub medical education platform')
        .setVersion('1.0')
        .addTag('auth', 'Authentication endpoints')
        .addTag('users', 'User management endpoints')
        .addTag('materials', 'Study materials endpoints')
        .addTag('progress', 'Progress tracking endpoints')
        .addTag('quizzes', 'Quiz and assessment endpoints')
        .addTag('units', 'Learning units endpoints')
        .addTag('notifications', 'User notification endpoints')
        .addTag('feedback', 'User feedback endpoints')
        .addTag('health', 'System health endpoints')
        .addBearerAuth({ type: 'http', scheme: 'bearer', bearerFormat: 'JWT' }, 'access-token')
        .setContact('API Support', 'https://example.com/support', '<EMAIL>')
        .setExternalDoc('Additional Documentation', 'https://example.com/docs')
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    const customOptions = {
        swaggerOptions: {
            persistAuthorization: true,
        },
        customSiteTitle: 'MedTrack Hub API Docs',
        customCss: '.swagger-ui .topbar { display: none }',
    };
    swagger_1.SwaggerModule.setup('api/docs', app, document, customOptions);
}
//# sourceMappingURL=swagger.setup.js.map