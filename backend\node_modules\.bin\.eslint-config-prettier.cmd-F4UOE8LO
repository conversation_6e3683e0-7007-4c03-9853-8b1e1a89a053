@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\medical\backend\node_modules\.pnpm\eslint-config-prettier@10.1.1_eslint@9.23.0\node_modules\eslint-config-prettier\bin\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\eslint-config-prettier@10.1.1_eslint@9.23.0\node_modules\eslint-config-prettier\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\eslint-config-prettier@10.1.1_eslint@9.23.0\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\medical\backend\node_modules\.pnpm\eslint-config-prettier@10.1.1_eslint@9.23.0\node_modules\eslint-config-prettier\bin\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\eslint-config-prettier@10.1.1_eslint@9.23.0\node_modules\eslint-config-prettier\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\eslint-config-prettier@10.1.1_eslint@9.23.0\node_modules;C:\Users\<USER>\medical\backend\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\eslint-config-prettier\bin\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\eslint-config-prettier\bin\cli.js" %*
)
