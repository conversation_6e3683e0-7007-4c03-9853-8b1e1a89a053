#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/ts-jest@29.3.1_@babel+core@_1bcdc6a2110a62890ad4e7ab70a0d901/node_modules/ts-jest/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/ts-jest@29.3.1_@babel+core@_1bcdc6a2110a62890ad4e7ab70a0d901/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/ts-jest@29.3.1_@babel+core@_1bcdc6a2110a62890ad4e7ab70a0d901/node_modules/ts-jest/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/ts-jest@29.3.1_@babel+core@_1bcdc6a2110a62890ad4e7ab70a0d901/node_modules:/mnt/c/Users/<USER>/medical/backend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../ts-jest/cli.js" "$@"
else
  exec node  "$basedir/../ts-jest/cli.js" "$@"
fi
