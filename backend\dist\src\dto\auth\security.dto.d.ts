export declare class SecuritySettingsDto {
    twoFactorEnabled?: boolean;
    loginNotifications?: boolean;
    activityNotifications?: boolean;
    sessionTimeout?: number;
}
export declare class ChangePasswordDto {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
}
export declare class TwoFactorSetupDto {
    userId: string;
}
export declare class VerifyTwoFactorDto {
    token: string;
}
export declare class EmailVerificationDto {
    token: string;
}
export declare class ResendVerificationDto {
    email: string;
}
export declare class RevokeSessionDto {
    sessionId: string;
}
export declare class AccountRecoveryDto {
    email: string;
}
export declare class VerifyRecoveryDto {
    token: string;
    answers: {
        [key: string]: string;
    };
}
