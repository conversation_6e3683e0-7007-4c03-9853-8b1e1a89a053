^C:\USERS\<USER>\MEDICAL\BACKEND\NODE_MODULES\.PNPM\@TENSORFLOW+TFJS-NODE@4.22._CFF3AE2E40E80BE74CB56D80AF0341DA\NODE_MODULES\@TENSORFLOW\TFJS-NODE\BUILD\RELEASE\TFJS_BINDING.NODE
call mkdir "C:\Users\<USER>\medical\backend\node_modules\.pnpm\@tensorflow+tfjs-node@4.22._cff3ae2e40e80be74cb56d80af0341da\node_modules\@tensorflow\tfjs-node\lib\napi-v8" 2>nul & set ERRORLEVEL=0 & copy /Y "C:\Users\<USER>\medical\backend\node_modules\.pnpm\@tensorflow+tfjs-node@4.22._cff3ae2e40e80be74cb56d80af0341da\node_modules\@tensorflow\tfjs-node\build\Release\tfjs_binding.node" "C:\Users\<USER>\medical\backend\node_modules\.pnpm\@tensorflow+tfjs-node@4.22._cff3ae2e40e80be74cb56d80af0341da\node_modules\@tensorflow\tfjs-node\lib\napi-v8\tfjs_binding.node"
if %errorlevel% neq 0 exit /b %errorlevel%
