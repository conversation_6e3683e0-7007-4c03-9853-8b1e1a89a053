"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddLearningHistoryAndPreferences1710000000001 = void 0;
class AddLearningHistoryAndPreferences1710000000001 {
    async up(queryRunner) {
        await queryRunner.query(`
            ALTER TABLE users
            ADD COLUMN IF NOT EXISTS learning_history jsonb,
            ADD COLUMN IF NOT EXISTS preferences jsonb;
        `);
        await queryRunner.query(`
            ALTER TABLE materials
            ADD COLUMN IF NOT EXISTS category varchar,
            ADD COLUMN IF NOT EXISTS difficulty float;
        `);
        await queryRunner.query(`
            CREATE INDEX IF NOT EXISTS idx_materials_category ON materials(category);
            CREATE INDEX IF NOT EXISTS idx_materials_difficulty ON materials(difficulty);
        `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
            DROP INDEX IF EXISTS idx_materials_category;
            DROP INDEX IF EXISTS idx_materials_difficulty;
        `);
        await queryRunner.query(`
            ALTER TABLE materials
            DROP COLUMN IF EXISTS category,
            DROP COLUMN IF EXISTS difficulty;
        `);
        await queryRunner.query(`
            ALTER TABLE users
            DROP COLUMN IF EXISTS learning_history,
            DROP COLUMN IF EXISTS preferences;
        `);
    }
}
exports.AddLearningHistoryAndPreferences1710000000001 = AddLearningHistoryAndPreferences1710000000001;
//# sourceMappingURL=1710000000001-AddLearningHistoryAndPreferences.js.map