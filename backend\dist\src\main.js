"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const app_module_1 = require("./app.module");
const logging_interceptor_1 = require("./common/interceptors/logging.interceptor");
const transform_interceptor_1 = require("./common/interceptors/transform.interceptor");
const all_exceptions_filter_1 = require("./common/filters/all-exceptions.filter");
const helmet_1 = __importDefault(require("helmet"));
const express_rate_limit_1 = require("express-rate-limit");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule, {
        logger: ['error', 'warn', 'log'],
        bufferLogs: true,
    });
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        transform: true,
        forbidNonWhitelisted: true,
        enableDebugMessages: process.env.NODE_ENV !== 'production',
        validationError: {
            target: false,
            value: process.env.NODE_ENV !== 'production'
        },
        exceptionFactory: (errors) => {
            const messages = errors.map(error => ({
                field: error.property,
                value: process.env.NODE_ENV !== 'production' ? error.value : undefined,
                constraints: error.constraints
            }));
            return {
                statusCode: 400,
                message: 'Validation failed',
                errors: messages
            };
        }
    }));
    app.useGlobalInterceptors(new logging_interceptor_1.LoggingInterceptor(), new transform_interceptor_1.TransformInterceptor());
    app.useGlobalFilters(new all_exceptions_filter_1.AllExceptionsFilter());
    app.enableVersioning({
        type: common_1.VersioningType.URI,
        defaultVersion: '1',
    });
    app.enableCors({
        origin: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : ['http://localhost:3000'],
        methods: ['GET', 'HEAD', 'PUT', 'PATCH', 'POST', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'Origin', 'X-Requested-With'],
        exposedHeaders: ['X-RateLimit-Limit', 'X-RateLimit-Remaining', 'X-RateLimit-Reset'],
        credentials: true,
        maxAge: 3600,
    });
    app.use((0, helmet_1.default)({
        contentSecurityPolicy: {
            directives: {
                defaultSrc: ["'self'"],
                styleSrc: ["'self'", "'unsafe-inline'"],
                scriptSrc: ["'self'", "'unsafe-inline'"],
                imgSrc: ["'self'", 'data:', 'https:'],
                connectSrc: ["'self'"],
                fontSrc: ["'self'", 'https:', 'data:'],
                objectSrc: ["'none'"],
                mediaSrc: ["'self'"],
                frameSrc: ["'none'"],
                formAction: ["'self'"],
                workerSrc: ["'self'", 'blob:'],
                baseUri: ["'self'"],
                manifestSrc: ["'self'"],
            },
        },
        crossOriginEmbedderPolicy: true,
        crossOriginOpenerPolicy: true,
        crossOriginResourcePolicy: { policy: "same-site" },
        dnsPrefetchControl: true,
        frameguard: { action: 'deny' },
        hidePoweredBy: true,
        hsts: {
            maxAge: 31536000,
            includeSubDomains: true,
            preload: true,
        },
        ieNoOpen: true,
        noSniff: true,
        referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
        xssFilter: true,
    }));
    const loginLimiter = (0, express_rate_limit_1.rateLimit)({
        windowMs: 15 * 60 * 1000,
        max: 5,
        message: 'Too many login attempts, please try again after 15 minutes',
        standardHeaders: true,
        legacyHeaders: false,
    });
    const apiLimiter = (0, express_rate_limit_1.rateLimit)({
        windowMs: 15 * 60 * 1000,
        max: 100,
        message: 'Too many requests from this IP, please try again after 15 minutes',
        standardHeaders: true,
        legacyHeaders: false,
    });
    app.use('/auth/login', loginLimiter);
    app.use('/auth/register', loginLimiter);
    app.use(apiLimiter);
    const port = process.env.PORT || 3002;
    await app.listen(port);
    const serverUrl = `http://localhost:${port}`;
    console.log(`=======================================================`);
    console.log(`🚀 MedTrack Hub Backend server is running on port: ${port}`);
    console.log(`📝 API Documentation: ${serverUrl}/api/docs`);
    console.log(`🔐 Auth endpoints:`);
    console.log(`   - POST ${serverUrl}/auth/register`);
    console.log(`   - POST ${serverUrl}/auth/login`);
    console.log(`   - GET ${serverUrl}/auth/profile`);
    console.log(`=======================================================`);
}
bootstrap();
//# sourceMappingURL=main.js.map