"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("../modules/auth/jwt-auth.guard");
let TestController = class TestController {
    getTestInfo() {
        return {
            status: 'ok',
            message: 'Test endpoint is working',
            timestamp: new Date().toISOString(),
        };
    }
    getModules() {
        return {
            modules: [
                {
                    name: 'auth',
                    status: 'active',
                    endpoints: ['/auth/login', '/auth/profile'],
                },
                {
                    name: 'materials',
                    status: 'active',
                    endpoints: ['/materials', '/materials/:id', '/materials/upload'],
                },
                {
                    name: 'users',
                    status: 'active',
                    endpoints: ['/users', '/users/:id'],
                },
                {
                    name: 'units',
                    status: 'active',
                    endpoints: ['/units', '/units/:id'],
                },
                {
                    name: 'progress',
                    status: 'active',
                    endpoints: ['/progress/:unitId', '/progress/user/:userId'],
                },
                {
                    name: 'quiz',
                    status: 'active',
                    endpoints: ['/quiz/unit/:unitId', '/quiz/submit', '/quiz/results/:userId/:unitId'],
                },
                {
                    name: 'notifications',
                    status: 'active',
                    endpoints: ['/notifications', '/notifications/:id'],
                },
            ],
        };
    }
    getModuleInfo(name) {
        const modules = {
            auth: {
                name: 'auth',
                description: 'Handles user authentication and authorization',
                endpoints: [
                    { path: '/auth/login', method: 'POST', description: 'Authenticate user' },
                    { path: '/auth/profile', method: 'GET', description: 'Get user profile' },
                ],
                dependencies: ['users'],
            },
            materials: {
                name: 'materials',
                description: 'Manages study materials',
                endpoints: [
                    { path: '/materials', method: 'GET', description: 'Get all materials' },
                    { path: '/materials/:id', method: 'GET', description: 'Get material by ID' },
                    { path: '/materials', method: 'POST', description: 'Create new material' },
                    { path: '/materials/:id', method: 'DELETE', description: 'Delete material' },
                    { path: '/materials/upload', method: 'POST', description: 'Upload material file' },
                ],
                dependencies: ['units'],
            },
            users: {
                name: 'users',
                description: 'Manages user accounts',
                endpoints: [
                    { path: '/users', method: 'GET', description: 'Get all users' },
                    { path: '/users', method: 'POST', description: 'Create new user' },
                ],
                dependencies: [],
            },
            units: {
                name: 'units',
                description: 'Manages learning units within materials',
                endpoints: [
                    { path: '/units', method: 'GET', description: 'Get all units' },
                    { path: '/units/:id', method: 'GET', description: 'Get unit by ID' },
                    { path: '/units', method: 'POST', description: 'Create new unit' },
                ],
                dependencies: ['materials', 'quiz'],
            },
            progress: {
                name: 'progress',
                description: 'Tracks user progress through units',
                endpoints: [
                    { path: '/progress/:unitId', method: 'POST', description: 'Update unit progress' },
                    { path: '/progress/user/:userId', method: 'GET', description: 'Get user progress' },
                ],
                dependencies: ['users', 'units'],
            },
            quiz: {
                name: 'quiz',
                description: 'Manages quizzes for units',
                endpoints: [
                    { path: '/quiz/unit/:unitId', method: 'GET', description: 'Get quiz for unit' },
                    { path: '/quiz/submit', method: 'POST', description: 'Submit quiz answers' },
                    { path: '/quiz/results/:userId/:unitId', method: 'GET', description: 'Get quiz results' },
                ],
                dependencies: ['units', 'progress'],
            },
            notifications: {
                name: 'notifications',
                description: 'Manages user notifications',
                endpoints: [
                    { path: '/notifications', method: 'GET', description: 'Get all notifications' },
                    { path: '/notifications', method: 'POST', description: 'Create new notification' },
                ],
                dependencies: ['users'],
            },
        };
        return modules[name] || { error: 'Module not found' };
    }
    checkAuth() {
        return {
            authenticated: true,
            timestamp: new Date().toISOString(),
        };
    }
    echo(body) {
        return {
            received: body,
            timestamp: new Date().toISOString(),
        };
    }
};
exports.TestController = TestController;
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], TestController.prototype, "getTestInfo", null);
__decorate([
    (0, common_1.Get)('modules'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], TestController.prototype, "getModules", null);
__decorate([
    (0, common_1.Get)('module/:name'),
    __param(0, (0, common_1.Param)('name')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Object)
], TestController.prototype, "getModuleInfo", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('auth-check'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], TestController.prototype, "checkAuth", null);
__decorate([
    (0, common_1.Post)('echo'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], TestController.prototype, "echo", null);
exports.TestController = TestController = __decorate([
    (0, common_1.Controller)('test')
], TestController);
//# sourceMappingURL=test.controller.js.map