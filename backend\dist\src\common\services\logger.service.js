"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoggerService = void 0;
const common_1 = require("@nestjs/common");
let LoggerService = class LoggerService extends common_1.Logger {
    constructor() {
        super(...arguments);
        this.isDevelopment = process.env.NODE_ENV === 'development';
    }
    error(message, stack, context) {
        const logEntry = this.createLogEntry('ERROR', message, { context });
        if (stack && this.isDevelopment) {
            logEntry.stack = stack;
        }
        super.error(JSON.stringify(logEntry));
    }
    warn(message, context) {
        const logEntry = this.createLogEntry('WARN', message, { context });
        super.warn(JSON.stringify(logEntry));
    }
    log(message, context) {
        const logEntry = this.createLogEntry('INFO', message, { context });
        super.log(JSON.stringify(logEntry));
    }
    debug(message, context) {
        if (this.isDevelopment) {
            const logEntry = this.createLogEntry('DEBUG', message, { context });
            super.debug(JSON.stringify(logEntry));
        }
    }
    createLogEntry(level, message, context) {
        return {
            timestamp: new Date().toISOString(),
            level,
            message,
            ...context,
            environment: process.env.NODE_ENV,
        };
    }
};
exports.LoggerService = LoggerService;
exports.LoggerService = LoggerService = __decorate([
    (0, common_1.Injectable)({ scope: common_1.Scope.TRANSIENT })
], LoggerService);
//# sourceMappingURL=logger.service.js.map