import { Repository, ObjectLiteral, DeepPartial } from 'typeorm';
export interface IBaseRepository<T extends ObjectLiteral> {
    findAll(): Promise<T[]>;
    findById(id: string): Promise<T | null>;
    findOne(options: any): Promise<T | null>;
    findMany(filter: Partial<T>): Promise<T[]>;
    create(data: DeepPartial<T>): Promise<T>;
    update(id: string, data: Partial<T>): Promise<T | null>;
    delete(id: string): Promise<boolean>;
    save(entity: T): Promise<T>;
}
export declare class BaseRepository<T extends ObjectLiteral> implements IBaseRepository<T> {
    protected readonly repository: Repository<T>;
    constructor(repository: Repository<T>);
    findAll(): Promise<T[]>;
    findById(id: string): Promise<T | null>;
    findOne(options: any): Promise<T | null>;
    findMany(filter: Partial<T>): Promise<T[]>;
    create(data: DeepPartial<T>): Promise<T>;
    update(id: string, data: Partial<T>): Promise<T | null>;
    delete(id: string): Promise<boolean>;
    save(entity: T): Promise<T>;
}
