"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsService = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = require("@nestjs/axios");
const config_1 = require("@nestjs/config");
const rxjs_1 = require("rxjs");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const unit_quiz_score_entity_1 = require("./unit-quiz-score.entity");
let AnalyticsService = class AnalyticsService {
    constructor(httpService, configService, unitQuizScoreRepo) {
        this.httpService = httpService;
        this.configService = configService;
        this.unitQuizScoreRepo = unitQuizScoreRepo;
        const url = this.configService.get('ANALYTICS_SERVICE_URL');
        if (!url) {
            throw new Error('ANALYTICS_SERVICE_URL is not defined');
        }
        this.analyticsUrl = url;
    }
    async getLearningPatterns(userId) {
        try {
            const { data } = await (0, rxjs_1.firstValueFrom)(this.httpService.get(`${this.analyticsUrl}/api/analytics/learning-patterns/${userId}`));
            return data;
        }
        catch (error) {
            throw new common_1.HttpException('Failed to fetch learning patterns', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getRecommendations(userId) {
        try {
            const { data } = await (0, rxjs_1.firstValueFrom)(this.httpService.get(`${this.analyticsUrl}/api/analytics/recommendations/${userId}`));
            return data;
        }
        catch (error) {
            throw new common_1.HttpException('Failed to fetch recommendations', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getPerformanceMetrics(userId) {
        try {
            const { data } = await (0, rxjs_1.firstValueFrom)(this.httpService.get(`${this.analyticsUrl}/api/analytics/performance/${userId}`));
            return data;
        }
        catch (error) {
            throw new common_1.HttpException('Failed to fetch performance metrics', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getPeerBenchmarks(userId) {
        try {
            const { data } = await (0, rxjs_1.firstValueFrom)(this.httpService.get(`${this.analyticsUrl}/api/analytics/benchmarks/${userId}`));
            return data;
        }
        catch (error) {
            throw new common_1.HttpException('Failed to fetch peer benchmarks', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async trackEvent(userId, eventType, data) {
        await (0, rxjs_1.firstValueFrom)(this.httpService.post(`${this.analyticsUrl}/events`, {
            userId,
            eventType,
            data,
            timestamp: new Date()
        }));
    }
    async trackPageView(userId, page, metadata) {
        await (0, rxjs_1.firstValueFrom)(this.httpService.post(`${this.analyticsUrl}/pageviews`, {
            userId,
            page,
            metadata,
            timestamp: new Date()
        }));
    }
    async trackError(userId, error, context) {
        await (0, rxjs_1.firstValueFrom)(this.httpService.post(`${this.analyticsUrl}/errors`, {
            userId,
            error: {
                message: error.message,
                stack: error.stack
            },
            context,
            timestamp: new Date()
        }));
    }
    async trackPerformance(userId, metrics) {
        await (0, rxjs_1.firstValueFrom)(this.httpService.post(`${this.analyticsUrl}/performance`, {
            userId,
            metrics,
            timestamp: new Date()
        }));
    }
    async recordUnitQuizScore(userId, unitId, score) {
    }
    async getUnitCompletionRate(unitId) {
    }
    async unitLevelGapAnalysis(userId, unitId) {
    }
};
exports.AnalyticsService = AnalyticsService;
exports.AnalyticsService = AnalyticsService = __decorate([
    (0, common_1.Injectable)(),
    __param(2, (0, typeorm_1.InjectRepository)(unit_quiz_score_entity_1.UnitQuizScore)),
    __metadata("design:paramtypes", [axios_1.HttpService,
        config_1.ConfigService,
        typeorm_2.Repository])
], AnalyticsService);
//# sourceMappingURL=analytics.service.js.map