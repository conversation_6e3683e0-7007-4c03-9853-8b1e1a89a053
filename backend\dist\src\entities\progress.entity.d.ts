import { User } from './user.entity';
import { Unit } from './unit.entity';
import { Material, MaterialType } from './materials.entity';
export declare class Progress {
    id: string;
    user: User;
    unit: Unit;
    material: Material;
    status: string;
    activity_type: MaterialType;
    last_accessed: Date;
    last_reviewed_at: Date;
    next_review_date: Date;
    ease_factor: number;
    interval: number;
    created_at: Date;
    updated_at: Date;
    is_completed: boolean;
}
