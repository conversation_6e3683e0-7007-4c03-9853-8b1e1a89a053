{"version": 3, "file": "1719999999999-AddUnitQuiz.js", "sourceRoot": "", "sources": ["../../../../src/database/migrations/1719999999999-AddUnitQuiz.ts"], "names": [], "mappings": ";;;AAAA,qCAA+F;AAE/F,MAAa,wBAAwB;IAC5B,KAAK,CAAC,EAAE,CAAC,WAAwB;QAEtC,MAAM,WAAW,CAAC,WAAW,CAAC,IAAI,eAAK,CAAC;YACtC,IAAI,EAAE,cAAc;YACpB,OAAO,EAAE;gBACP,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE;gBAC5F,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE;gBAChC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE;gBAClC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,MAAM,EAAE;gBACtC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE;gBACxD,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE;gBAC1D,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE;aAC3D;SACF,CAAC,CAAC,CAAC;QAGJ,MAAM,WAAW,CAAC,gBAAgB,CAAC,cAAc,EAAE,IAAI,yBAAe,CAAC;YACrE,WAAW,EAAE,CAAC,QAAQ,CAAC;YACvB,qBAAqB,EAAE,CAAC,IAAI,CAAC;YAC7B,mBAAmB,EAAE,OAAO;YAC5B,QAAQ,EAAE,SAAS;SACpB,CAAC,CAAC,CAAC;QAGJ,MAAM,WAAW,CAAC,SAAS,CAAC,gBAAgB,EAAE,IAAI,qBAAW,CAAC;YAC5D,IAAI,EAAE,YAAY;YAClB,IAAI,EAAE,MAAM;YACZ,UAAU,EAAE,IAAI;SACjB,CAAC,CAAC,CAAC;QAEJ,MAAM,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,yBAAe,CAAC;YACvE,WAAW,EAAE,CAAC,YAAY,CAAC;YAC3B,qBAAqB,EAAE,CAAC,IAAI,CAAC;YAC7B,mBAAmB,EAAE,cAAc;YACnC,QAAQ,EAAE,SAAS;SACpB,CAAC,CAAC,CAAC;IACN,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QAExC,MAAM,KAAK,GAAG,MAAM,WAAW,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;QAC3D,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,EAAE,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,EAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACxG,IAAI,EAAE;gBAAE,MAAM,WAAW,CAAC,cAAc,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;QACjE,CAAC;QACD,MAAM,WAAW,CAAC,UAAU,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC;QAG7D,MAAM,WAAW,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;IAC9C,CAAC;CACF;AAnDD,4DAmDC"}